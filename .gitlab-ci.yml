variables:
  IMAGE_TAG: ${CI_REGISTRY}/${CI_PROJECT_PATH}:latest-${CI_COMMIT_REF_NAME}

stages:
  - build
  - deploy

.docker_base:
  image: docker/compose:alpine-1.29.2
  before_script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
  after_script:
    - docker logout ${CI_REGISTRY}


build_image:
  extends: .docker_base
  stage: build 
  services:
    - docker:26.1.1-dind-alpine3.19
  rules:
    - if: '$SKIP_BUILD'
      when: never
    - if: '$CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_BRANCH == "staging" || $CI_COMMIT_BRANCH == "master"'
  script:
    - docker build -t ${IMAGE_TAG} --pull .
    - docker push ${IMAGE_TAG}
  # tags:
  #   - testing-docker

.deploy:
  extends: .docker_base
  stage: deploy 
  script:
    - cat ${ENV} > .env
    - mkdir -p /data/service-finance
    - cat $GOOGLE_SERVICE_ACCOUNT > /data/service-finance/google_service_account.json
    - docker pull ${IMAGE_TAG}
    - docker container rm -f $CI_PROJECT_NAME || true
    - docker run --restart unless-stopped --name ${CI_PROJECT_NAME} --network uniq-network --env-file .env -v /docker/runner/data/service-finance/:/credential/ --log-driver=gcplogs  -d ${IMAGE_TAG}

deployDev:
  extends: .deploy 
  environment:
    name: development
  variables:
    CI_PROJECT_NAME: uniqpos-service-finance
  only:
    - dev
  tags:
    - testing-docker

deployStaging:
  extends: 
    - .deploy 
  environment:
    name: staging
  only:
    - staging
  tags:
    - staging

deployProduction:
  extends: 
    - .deploy 
  environment:
    name: production
  only:
    - master
  tags:
    - production

# CI CD Old Version:
deploy_staging:
  stage: deploy
  image: docker/compose:1.25.5
  environment:
    name: staging
  when: manual
  before_script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
  script:
    - cat ${ENV} > .env
    - mkdir -p /data/service-finance
    - cat $GOOGLE_SERVICE_ACCOUNT > /data/service-finance/google_service_account.json
    - docker pull ${IMAGE_TAG}
    - docker container rm -f $CI_PROJECT_NAME || true
    - docker run --restart unless-stopped --name ${CI_PROJECT_NAME} --network uniq-network --env-file .env -p 1924:80 --log-driver=gcplogs --log-opt gcp-project=uniq-187911 -d ${IMAGE_TAG}
    - cat $GOOGLE_SERVICE_ACCOUNT > google_service_account.json
    - docker cp google_service_account.json ${CI_PROJECT_NAME}:/google_service_account.json
  after_script:
    - docker logout ${CI_REGISTRY}
  only:
    - staging
  tags:
    - staging