package model

type AccountGeneral struct {
	ID               int64  `json:"id"`
	TypeFkid         int    `json:"type_fkid"`
	Type             string `json:"type"`
	AccountFkid      int    `json:"account_fkid"`
	SalesAccountFkid int    `json:"sales_account_fkid"`
	PrcAccountFkid   int    `json:"prc_account_fkid"`
	DefaultKeyFkid   int    `json:"default_key_fkid"`
	Status           string `json:"status"`
	AdminFkid        int    `json:"admin_fkid"`
}

func (a AccountGeneral) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"id":                 a.ID,
		"type_fkid":          a.TypeFkid,
		"type":               a.Type,
		"account_fkid":       a.AccountFkid,
		"sales_account_fkid": a.SalesAccountFkid,
		"prc_account_fkid":   a.PrcAccountFkid,
		"default_key_fkid":   a.<PERSON><PERSON>ult<PERSON>eyFkid,
		"status":             a.Status,
		"admin_fkid":         a.AdminFkid,
	}
}
