package model

type SystemSubscribe struct {
	ID                 int    `json:"id"`
	BillingFkid        int    `json:"billing_fkid"`
	BillingDetailFkid  int    `json:"billing_detail_fkid"`
	Feature            string `json:"feature"`
	SysServiceFkid     int    `json:"sys_service_fkid"`
	ServicePeriod      int    `json:"service_period"`
	AdminFkid          int    `json:"admin_fkid"`
	ServiceTimeStart   int64  `json:"service_time_start"`
	ServiceTimeExpired int64  `json:"service_time_expired"`
}
