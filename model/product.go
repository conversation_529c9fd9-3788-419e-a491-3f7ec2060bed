package model

type PurchaseReportCategoryEntity struct {
	PurchaseReportCategoryID int    `json:"purchase_report_category_id,omitempty"`
	Name                     string `json:"name,omitempty"`
	IsOperationalCost        int    `json:"is_operationalcost,omitempty"`
	AdminFKID                int    `json:"admin_fkid,omitempty"`
	DataCreated              string `json:"data_created,omitempty"`
	DataModified             string `json:"data_modified,omitempty"`
	DataStatus               string `json:"data_status,omitempty"`
}
