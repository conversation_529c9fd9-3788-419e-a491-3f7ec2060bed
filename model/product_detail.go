package model

type ProductDetailEntity struct {
	ProductDetailID    int         `json:"product_detail_id,omitempty"`
	ProductFkid        int         `json:"product_fkid,omitempty"`
	OutletFkid         int         `json:"outlet_fkid,omitempty"`
	PriceBuy           int64       `json:"price_buy,omitempty"`
	PriceSell          int         `json:"price_sell,omitempty"`
	TransferMarkupType string      `json:"transfer_markup_type,omitempty"`
	TransferMarkup     int         `json:"transfer_markup,omitempty"`
	DataModified       int64       `json:"data_modified,omitempty"`
	VariantFkid        interface{} `json:"variant_fkid,omitempty"`
	Stock              string      `json:"stock,omitempty"`
	StockQty           int         `json:"stock_qty,omitempty"`
	DataStatus         string      `json:"data_status,omitempty"`
}
