package model

type ProductionEntity struct {
	ProductionID      int64  `json:"production_id,omitempty"`
	ItemBreakdownFKID int64  `json:"itembreakdown_fkid,omitempty"`
	ProductDetailFKID int64  `json:"product_detail_fkid,omitempty"`
	OutletFKID        int64  `json:"outlet_fkid,omitempty"`
	QtyRecipe         int    `json:"qty_recipe,omitempty"`
	QtyPrimary        int    `json:"qty_primary,omitempty"`
	EmployeeFKID      int64  `json:"employee_fkid,omitempty"`
	AdminFKID         int64  `json:"admin_fkid,omitempty"`
	DateInput         string `json:"date_input,omitempty"`
	DataCreated       int64  `json:"data_created,omitempty"`
	DataModified      int64  `json:"data_modified,omitempty"`
	DataDeleteAt      string `json:"data_delete_at,omitempty"`
	Transferred       int    `json:"transferred,omitempty"`
	//@non columns:
	ProductFkid int `json:"product_fkid,omitempty"`
}
