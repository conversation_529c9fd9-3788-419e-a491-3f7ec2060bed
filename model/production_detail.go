package model

// enum('ingridient','ingredient','endproduct','residual')
const (
	ProductionTypeIngredient = "ingredient"
	ProductionTypeEndProduct = "endproduct"
	ProductionTypeResidual   = "residual"
)

type ProductionDetailEntity struct {
	ProductionDetailID int64   `json:"productiondetail_id"`
	ProductionFKID     int64   `json:"production_fkid"`
	ProductFKID        int64   `json:"product_fkid"`
	ProductDetailFKID  int64   `json:"product_detail_fkid"`
	Qty                float32 `json:"qty"`
	DetailType         string  `json:"detail_type"`
	DataCreated        int64   `json:"data_created"`
	DataModified       int64   `json:"data_modified"`
}

func (p ProductionDetailEntity) ToMap() map[string]interface{} {
	dataMap := map[string]interface{}{
		"productiondetail_id": p.ProductionDetailID,
		"production_fkid":     p.ProductionFKID,
		"product_fkid":        p.ProductF<PERSON>,
		"product_detail_fkid": p.ProductDetailFKID,
		"qty":                 p.Qty,
		"detail_type":         p.DetailType,
		"data_created":        p.DataCreated,
		"data_modified":       p.DataModified,
	}
	return dataMap
}
