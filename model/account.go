package model

type Account struct {
	AccountID           int    `json:"account_id"`
	AccountCategoryFkid int    `json:"account_category_fkid"`
	Code                string `json:"code"`
	Name                string `json:"name"`
	Keterangan          string `json:"keterangan"`
	AdminFkid           int    `json:"admin_fkid"`
	CreatedAt           int64  `json:"created_at"`
	UpdatedAt           int64  `json:"updated_at"`
	DataStatus          int    `json:"data_status"`
	Lock                int    `json:"lock"`
	AccountFkid         int    `json:"account_fkid"`
	Saldo               int    `json:"saldo"`
}

// convert Account to map
func (a *Account) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"account_id":            a.AccountID,
		"account_category_fkid": a.AccountCategoryFkid,
		"code":                  a.Code,
		"name":                  a.Name,
		"keterangan":            a.<PERSON>,
		"admin_fkid":            a.<PERSON><PERSON><PERSON>,
		"created_at":            a.<PERSON>t,
		"updated_at":            a.UpdatedAt,
		"data_status":           a.DataStatus,
		"lock":                  a.<PERSON>,
		"account_fkid":          a.AccountFkid,
		"saldo":                 a.Saldo,
	}
}
