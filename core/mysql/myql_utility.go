package mysql

import (
	"fmt"
	"reflect"
	"regexp"
	"strings"
)

func MapParam(sql string, params map[string]interface{}) (string, []interface{}) {
	r, _ := regexp.Compile("@[a-zA-Z]+")

	result := make([]interface{}, 0)
	for {
		key := r.FindString(sql)
		if key != "" {
			key = strings.Replace(key, "@", "", 1)
			sql = strings.Replace(sql, fmt.Sprintf("(@%s)", key), fmt.Sprintf("@%s", key), 1)
			if s := reflect.ValueOf(params[key]); s.Kind() == reflect.Slice {
				arrLenght := 0
				for i := 0; i < s.Len(); i++ {
					if values := reflect.ValueOf(s.Index(i).Interface()); values.Kind() == reflect.Slice {
						for j := 0; j < values.Len(); j++ {
							result = append(result, values.Index(j).Interface())
							arrLenght += 1
						}
					} else {
						result = append(result, s.Index(i).Interface())
						arrLenght += 1
					}
				}
				sql = strings.Replace(sql, fmt.Sprintf("@%s", key), WhereIn(arrLenght), 1)
			} else {
				if params[key] != nil {
					result = append(result, params[key])
				} else {
					fmt.Printf("Map Data with key '%s' Not Found! \n", key)
				}
				sql = strings.Replace(sql, fmt.Sprintf("@%s", key), " ? ", 1)
			}
		} else {
			break
		}
	}
	return sql, result
}

func WhereIn(size int) string {
	whereIn := strings.Repeat("?,", size)
	whereIn = strings.TrimRight(whereIn, ",")
	return fmt.Sprintf("(%s)", whereIn)
}
