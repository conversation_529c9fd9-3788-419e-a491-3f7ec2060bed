package google

import (
	"cloud.google.com/go/pubsub"
	"context"
	"fmt"
	"sync"
)

func Subscribe(subsId string, action func(data []byte) bool) error {
	client := pubsubClient
	if client == nil {
		fmt.Println("pubsub client has not been initialized")
		return fmt.Errorf("pubsub client has not been initialized")
	}

	fmt.Println("pubsub subscribed to id: ", subsId)

	var mu sync.Mutex
	ctx := context.Background()
	sub := client.Subscription(subsId)
	cctx, _ := context.WithCancel(ctx)
	err := sub.Receive(cctx, func(ctx context.Context, msg *pubsub.Message) {
		mu.Lock()
		defer mu.Unlock()
		fmt.Printf("pubsub receive message, subs id: %s | id: %s | %s \n", subsId, msg.ID, string(msg.Data))
		if action(msg.Data) {
			fmt.Printf("pubsub ack? : %v | id: %s \n", true, msg.ID)
			msg.Ack()
		}
	})

	fmt.Printf("subsribe to %s error: %v \n", subsId, err)
	return err
}
