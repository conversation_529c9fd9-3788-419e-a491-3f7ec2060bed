package google

import (
	"cloud.google.com/go/pubsub"
	"context"
	"encoding/json"
	"fmt"
	"gitlab.com/uniqdev/backend/service-finance/core/util/reader"
	"os"
)

var pubsubClient *pubsub.Client

func init() {
	credPath := os.Getenv("GOOGLE_APPLICATION_CREDENTIALS")
	fmt.Println(credPath)
	var err error
	ctx := context.Background()

	projectId := os.Getenv("PROJECT_ID")
	if projectId == "" {
		credential, err := reader.ReadFile(credPath)
		if err == nil {
			var serviceAccount struct {
				ProjectID string `json:"project_id"`
			}
			err = json.Unmarshal([]byte(credential), &serviceAccount)
			if err == nil {
				projectId = serviceAccount.ProjectID
			}
		} else {
			fmt.Println("reading credential file err: ", err)
		}
	}

	fmt.Println("project id: ", projectId)
	pubsubClient, err = pubsub.NewClient(ctx, projectId)
	if err != nil {
		fmt.Println("init pubsub client err ", err)
	}

	fmt.Println("sdk init finish...")
}
