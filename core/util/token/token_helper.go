package token

import (
	"github.com/gofiber/fiber/v2"
	"gitlab.com/uniqdev/backend/service-finance/domain"
)

var UserData domain.User

func GetUserSessionOfFiber(c *fiber.Ctx) domain.User {
	return domain.User{
		Name:       c.Get("name"),
		Email:      c.Get("email"),
		Phone:      c.Get("phone"),
		UserType:   c.Get("user_type"),
		UserId:     c.Get("user_id"),
		BusinessId: c.Get("business_id"),
	}
}
