package cast

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"

	"gitlab.com/uniqdev/backend/service-finance/core/log"
)

func MapArrayToStruct(maps []map[string]interface{}, variable interface{}) error {
	structField := reflect.TypeOf(variable).Elem()
	structArray := reflect.ValueOf(variable).Elem()

	for _, m := range maps {
		newStruct := reflect.New(structField.Elem()).Elem()
		for k, v := range m {
			setField(newStruct, k, v)
		}
		structArray.Set(reflect.Append(structArray, newStruct))
	}

	return nil
}

func MapToStruct(m map[string]interface{}, s interface{}) error {
	for k, v := range m {
		setField(s, k, v)
	}
	return nil
}

func setField(m interface{}, key string, value interface{}) {
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("key %s, value %v, err : %v\n", key, value, r)
		}
	}()
	if value == nil {
		return
	}

	var structValue reflect.Value
	switch res := m.(type) {
	case reflect.Value:
		structValue = res
	default:
		structValue = reflect.ValueOf(m).Elem()
	}

	structFieldValue := structValue.FieldByName(key)

	//if key not match, search for json tag
	if !structFieldValue.IsValid() {
		//fmt.Println("num field: ", structValue.NumField())
		for i := 0; i < structValue.NumField(); i++ {
			field := structValue.Type().Field(i)
			//fmt.Println("field: ", ToJson(field))
			if field.Anonymous {
				structAnonym := structValue.Field(i)
				structFieldValue = structAnonym.FieldByName(key)
				if !structFieldValue.IsValid() {
					isFound := false
					for j := 0; j < structAnonym.NumField(); j++ {
						fieldAnonym := structAnonym.Type().Field(j)
						if v, ok := fieldAnonym.Tag.Lookup("json"); ok {
							if v == key || strings.HasPrefix(v, fmt.Sprintf("%s,", key)) {
								structFieldValue = structAnonym.FieldByName(fieldAnonym.Name)
								isFound = true
								break
							}
						}
					}
					if isFound {
						break
					}
				}
			} else if v, ok := field.Tag.Lookup("json"); ok {
				if v == key || strings.HasPrefix(v, fmt.Sprintf("%s,", key)) {
					structFieldValue = structValue.FieldByName(field.Name)
					break
				}
			}
		}
	}

	if !structFieldValue.IsValid() {
		//fmt.Printf("no such field: %s in obj\n", key)
		return
	}

	if !structFieldValue.CanSet() {
		fmt.Printf("can not set %s field value\n", key)
		return
	}

	structFieldType := structFieldValue.Type()
	val := reflect.ValueOf(value)

	//if data type from struct and map different, convert it
	if structFieldType != val.Type() {
		switch structFieldType.Kind() {
		case reflect.String:
			val = reflect.ValueOf(ToString(value))
		case reflect.Int:
			val = reflect.ValueOf(ToInt(value))
		default:

			// log.IfError(fmt.Errorf("field %s type didn't match obj field type, type is %v while value is %v", key, structFieldType, val.Type()))
			fmt.Printf(" %v --- [cast error] field %s type didn't match obj field type, type is %v while value is %v\n", log.GetCaller("repository"), key, structFieldType, val.Type())
			return
		}
	}

	structFieldValue.Set(val)
}

func ToString(data interface{}) string {
	switch v := data.(type) {
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case float32:
		return fmt.Sprintf("%f", v)
	case float64:
		return strconv.FormatFloat(v, 'f', 2, 64)
	case string:
		return v
	case []uint8:
		return string(v)
	case []interface{}:
		return fmt.Sprintf("%s", v[0])
	case nil:
		return ""
	default:
		fmt.Println("[ToString] - Invalid recognize data type toString. Type : ", reflect.TypeOf(data), " | Data : ", data)
		return fmt.Sprintf("%s", data)
	}
}

func ToInt(data interface{}) int {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	result, err := strconv.Atoi(dataStr)
	if err != nil {
		switch i := data.(type) {
		case float32:
			return int(i)
		case float64:
			return int(i)
		default:
			fmt.Printf("failed converting '%v' to int, type is %v\n", data, reflect.TypeOf(data))
			return 0
		}
	} else {
		return result
	}
}

func ToInt64(data interface{}) int64 {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	result, err := strconv.ParseInt(dataStr, 10, 64)
	if err != nil {
		return 0
	} else {
		return result
	}
}

func JsonToStruct(data interface{}, structVariable interface{}) interface{} {
	var err error
	switch v := data.(type) {
	case []byte:
		err = json.Unmarshal(v, &structVariable)
		if err != nil {
			fmt.Printf("failed converting '%v' to struct, type is %v\n", data, reflect.TypeOf(data))
			return nil
		}
		return structVariable
	case string:
		err = json.Unmarshal([]byte(v), &structVariable)
		if err != nil {
			fmt.Printf("failed converting '%v' to struct, type is %v\n", data, reflect.TypeOf(data))
			return nil
		}
		return structVariable
	default:
		fmt.Printf("failed converting '%v' to struct, type is %v\n", data, reflect.TypeOf(data))
		return nil
	}
}

func ToJson(data interface{}) string {
	result, err := json.Marshal(data)
	if err != nil {
		fmt.Println("failed converting to json: ", err)
	}
	return string(result)
}

func ToMap(data interface{}) map[string]interface{} {
	result := make(map[string]interface{})

	if reflect.TypeOf(data).Kind() != reflect.Struct {
		return result
	}
	structType := reflect.TypeOf(data)
	for i := 0; i < structType.NumField(); i++ {
		key := structType.Field(i).Name
		if v, ok := structType.Field(i).Tag.Lookup("json"); ok {
			key = v
			if strings.Contains(v, ",") {
				key = v[:strings.Index(v, ",")]
			}
		}
		result[key] = fmt.Sprintf("%v", reflect.ValueOf(data).Field(i))
	}
	return result
}

func JsonToMap(data interface{}) (map[string]interface{}, error) {
	var objmap map[string]interface{}
	var err error
	switch v := data.(type) {
	case error:
		s := v.Error()
		err = json.Unmarshal([]byte(s), &objmap)
		return objmap, err
	case string:
		err = json.Unmarshal([]byte(v), &objmap)
		return objmap, err
	}

	return objmap, err
}
