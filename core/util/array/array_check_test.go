package array

import "testing"

func TestIn(t *testing.T) {
	type args struct {
		data    interface{}
		compare interface{}
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{"test-equal", args{data: []string{"a", "b"}, compare: "b"}, true},
		{"test-Not-equal", args{data: []string{"budi", "andi"}, compare: "ary"}, false},
		{"test-equal-int", args{data: []int{405, 309}, compare: 405}, true},
		{"test-Not-equal-int", args{data: []int{405, 309, 777}, compare: 10}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := In(tt.args.data, tt.args.compare); got != tt.want {
				t.<PERSON>("In() = %v, want %v", got, tt.want)
			}
		})
	}
}
