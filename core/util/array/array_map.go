package array

import (
	"reflect"

	"gitlab.com/uniqdev/backend/service-finance/core/util/cast"
)

func FlatMap(data []map[string]interface{}, key string) map[string]map[string]interface{} {
	result := make(map[string]map[string]interface{})
	for _, row := range data {
		if row[key] == nil {
			continue
		}
		result[cast.ToString(row[key])] = row
	}
	return result
}

func Get<PERSON>eys(data interface{}) []interface{} {
	result := make([]interface{}, 0)
	if reflect.TypeOf(data).Kind() == reflect.Map {
		v := reflect.ValueOf(data)
		for _, key := range v.MapKeys() {
			result = append(result, key.Interface())
		}
	}
	return result
}

func RemoveEmptyKeys(m map[string]interface{}) {
	for k, v := range m {
		if v == nil {
			delete(m, k)
		}
		switch val := v.(type) {
		case string:
			if val == "" {
				delete(m, k)
			}
		case map[string]interface{}:
			RemoveEmptyKeys(val)
			if len(val) == 0 {
				delete(m, k)
			}
		case []interface{}:
			for i, u := range val {
				switch u := u.(type) {
				case map[string]interface{}:
					RemoveEmptyKeys(u)
					if len(u) == 0 {
						val[i] = nil
					}
				case string:
					if u == "" {
						val[i] = nil
					}
				}
			}
			if len(val) == 0 {
				delete(m, k)
			}
		}
	}
}
