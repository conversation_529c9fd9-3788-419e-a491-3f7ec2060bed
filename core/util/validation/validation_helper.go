package validation

import (
	"encoding/json"
	"fmt"
	"github.com/go-playground/locales"
	"github.com/go-playground/locales/en"
	"github.com/go-playground/locales/id"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	enTranslations "github.com/go-playground/validator/v10/translations/en"
	idTranslations "github.com/go-playground/validator/v10/translations/id"
	"log"
	"reflect"
	"strings"
)

var validate *validator.Validate
var Language = "id"

func Struct(structData interface{}) map[string]interface{} {
	//validate.
	validate = validator.New()

	//translation of validation
	var lang locales.Translator
	var trans ut.Translator
	var transFound bool

	switch Language {
	case "id":
		lang = id.New()
		uni := ut.New(lang, lang)
		trans, transFound = uni.GetTranslator(Language)
		if !transFound {
			log.Fatal("translator %s not found", Language)
		}
		idTranslations.RegisterDefaultTranslations(validate, trans)
	case "en":
		lang = en.New()
		uni := ut.New(lang, lang)
		trans, transFound = uni.GetTranslator(Language)
		if !transFound {
			log.Fatal("translator %s not found", Language)
		}
		enTranslations.RegisterDefaultTranslations(validate, trans)
	}

	// register function to get tag name from json tags.
	validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})

	//validation
	err := validate.Struct(structData)
	errList := map[string]interface{}{}
	if err != nil {
		for _, e := range err.(validator.ValidationErrors) {
			errTxt := e.Translate(trans)
			key := e.Field()
			errList[key] = errTxt
		}
	}

	return errList
}

func IsError(data interface{}) bool {
	switch v := data.(type) {
	case map[string]interface{}:
		return (len(v) > 0)
	}

	fmt.Printf("cannot check error of %s.", data)
	return true
}

func IsJson(data interface{}) bool {
	var js map[string]interface{}

	switch v := data.(type) {
	case string:
		return json.Unmarshal([]byte(v), &js) == nil
	case []byte:
		return json.Unmarshal(v, &js) == nil
	default:
		fmt.Printf("failed converting '%v' to int, type is %v\n", data, reflect.TypeOf(data))
		return false
	}

	//https://stackoverflow.com/questions/22128282/how-to-check-string-is-in-json-format
	//var js string
	//return json.Unmarshal([]byte(s), &js) == nil
}
