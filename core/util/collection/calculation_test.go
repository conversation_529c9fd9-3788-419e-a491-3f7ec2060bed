package collection

import "testing"

func Test_collection_SumBy(t *testing.T) {
	type fields struct {
		data []interface{}
	}
	type args struct {
		f   func(interface{}, string) int
		key string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   int
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := collection{
				data: tt.fields.data,
			}
			if got := c.SumBy(tt.args.f, tt.args.key); got != tt.want {
				t.<PERSON>rf("collection.SumBy() = %v, want %v", got, tt.want)
			}
		})
	}
}
