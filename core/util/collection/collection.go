package collection

import (
	"fmt"
	"reflect"
)

type collection struct {
	data []interface{}
}

func ArrayOf(data ...interface{}) []interface{} {
	result := make([]interface{}, 0)
	for _, row := range data {
		fmt.Println(row, reflect.TypeOf(row).Kind())
		if reflect.TypeOf(row).Kind() == reflect.Slice {
			result = append(result, ToInterfaceArray(row)...)
		} else {
			result = append(result, row)
		}
	}

	return result
}

func ToInterfaceArray(data interface{}) []interface{} {
	if reflect.TypeOf(data).Kind() != reflect.Slice {
		return []interface{}{data}
	}

	result := make([]interface{}, 0)
	switch v := data.(type) {
	case []string:
		for _, row := range v {
			result = append(result, row)
		}
	case []int:
		for _, row := range v {
			result = append(result, row)
		}
	case []int32:
		for _, row := range v {
			result = append(result, row)
		}
	case []int64:
		for _, row := range v {
			result = append(result, row)
		}
	case []float32:
		for _, row := range v {
			result = append(result, row)
		}
	case []float64:
		for _, row := range v {
			result = append(result, row)
		}
	default:
		fmt.Println("unknown slice of", reflect.TypeOf(data))
	}

	return result
}
