package config

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/uniqdev/backend/service-finance/core/util/cast"
	"gitlab.com/uniqdev/backend/service-finance/domain"
)

func AuthMiddleware(c *fiber.Ctx) error {
	requestToken := c.Get("Authorization")
	tokenData := strings.Split(requestToken, ".")
	if len(tokenData) != 3 {
		fmt.Println("Authorization header is not provided")
		return c.SendStatus(401)
	}

	//get token data
	tokenBody := tokenData[1] //jwt's payload

	//decode token
	decoded, _ := base64.RawStdEncoding.DecodeString(tokenBody)
	decodedJson := string(decoded)
	claims, _ := cast.JsonToMap(decodedJson)
	claims = claims["data"].(map[string]interface{})
	dataUser, _ := json.Marshal(claims["user"])
	dataRole, _ := json.Marshal(claims["user_role"])
	fmt.Println("data user: ", string(dataUser))

	//share token data
	var (
		user domain.User
		role domain.UserRole
	)

	// token data :: user
	cast.JsonToStruct(dataUser, &user)

	// token data :: user_role
	cast.JsonToStruct(dataRole, &role)

	var userMap map[string]interface{}
	cast.JsonToStruct(dataUser, &userMap)
	//set user data in header request
	for k, v := range userMap {
		c.Request().Header.Set(k, cast.ToString(v))
	}

	c.Request().Header.Set("outlet_access", role.OutletAccess)

	if user.UserId == "" {
		fmt.Println("invalid token on manual decode token")
		return c.SendStatus(401)
	}

	return c.Next()
}
