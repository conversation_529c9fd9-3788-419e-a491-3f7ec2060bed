package config

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	_ "github.com/go-sql-driver/mysql"
)

func GetMySqlConn() *sql.DB {
	username := os.<PERSON>env("DB_USERNAME")
	password := os.<PERSON>env("DB_PASSWORD")
	host := os.Getenv("DB_HOST")
	db := os.Getenv("DB_NAME")
	port := os.Getenv("DB_PORT")

	conn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s", username, password, host, port, db)
	dbConn, err := sql.Open("mysql", conn)

	if err != nil {
		log.Fatal(err)
	}

	err = dbConn.Ping()
	if err != nil {
		log.Fatal(err)
	}
	fmt.Println("database: ", db, "host: ", host)
	return dbConn
}

func CloseMySqlConn(dbConn *sql.DB) {
	err := dbConn.Close()
	if err != nil {
		log.Fatal(err)
	}
}
