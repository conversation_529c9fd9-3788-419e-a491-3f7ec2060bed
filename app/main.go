package main

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/go-co-op/gocron"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/joho/godotenv"
	"gitlab.com/uniqdev/backend/service-finance/app/config"
	"gitlab.com/uniqdev/backend/service-finance/core/log"
	"gitlab.com/uniqdev/backend/service-finance/core/util/cast"
	"gitlab.com/uniqdev/backend/service-finance/core/util/validation"
	"gitlab.com/uniqdev/backend/service-finance/domain"
	mysqlAccount "gitlab.com/uniqdev/backend/service-finance/module/account/repository/mysql"
	usecaseAccount "gitlab.com/uniqdev/backend/service-finance/module/account/usecase"
	authHttp "gitlab.com/uniqdev/backend/service-finance/module/auth/delivery/http"
	authRepoMysql "gitlab.com/uniqdev/backend/service-finance/module/auth/repository/mysql"
	authUseCase "gitlab.com/uniqdev/backend/service-finance/module/auth/usecase"
	bukuBesarHttp "gitlab.com/uniqdev/backend/service-finance/module/buku_besar/delivery/http"
	bukuBesarRepoMySql "gitlab.com/uniqdev/backend/service-finance/module/buku_besar/repository/mysql"
	bukuBesarUseCase "gitlab.com/uniqdev/backend/service-finance/module/buku_besar/usecase"
	journalHttp "gitlab.com/uniqdev/backend/service-finance/module/journal/delivery/http"
	journalRepoMySql "gitlab.com/uniqdev/backend/service-finance/module/journal/repository/mysql"
	journalUseCase "gitlab.com/uniqdev/backend/service-finance/module/journal/usecase"
	mysqlProduct "gitlab.com/uniqdev/backend/service-finance/module/product/repository/mysql"
	"gitlab.com/uniqdev/backend/service-finance/module/pubsub"
)

func init() {
	err := godotenv.Load()
	if err != nil {
		fmt.Println("error loading .env file")
	}
	log.AddHook(&log.SlackHook{
		HookUrl: os.Getenv("SLACK_HOOK_URL"),
		Channel: os.Getenv("SLACK_CHANNEL"),
	})
}

func main() {
	//get connetion
	dbConn := config.GetMySqlConn()
	defer config.CloseMySqlConn(dbConn)

	//cron job
	cron := gocron.NewScheduler(time.UTC)

	productRepo := mysqlProduct.NewMysqlProductRepository(dbConn)
	journal := journalUseCase.NewJournalUseCase(journalRepoMySql.NewMysqlJournalRepository(dbConn), productRepo, cron)
	journal.RunJob()

	//load usecase
	authUseCase := authUseCase.NewAuthUseCase(authRepoMysql.NewMysqlAuthRepository(dbConn))
	account := usecaseAccount.NewAccountUseCase(mysqlAccount.NewMysqlAccountRepository(dbConn))

	if args := os.Args; len(args) > 1 {
		switch args[2] {
		case "account":
			account.CreateDefaultAccount(318)
			fmt.Println(args)
		case "journal":
			salesIds := []string{"1IS08OSN563KO"}
			for _, id := range salesIds {
				journal.ReceiveNewSales(id)
			}
		case "production":
			journal.ReceiveNewProduction(38489)
		case "failure":
			journal.RunInvalidJournal()
		case "missing":
			journal.CheckMissingJournals(domain.MissingJournalParam{
				StartTime: *************,
				EndTime:   *************,
				AdminId:   10,
			})
		default:
			fmt.Println("Invalid argument")
		}

		panic(fmt.Errorf("only testing.... %s", args[2]))
	}

	//http ------
	app := fiber.New()
	app.Use(cors.New())
	app.Static("/", "./app/static")
	app.Get("/", func(c *fiber.Ctx) error {
		return c.SendString("Hy, Iam your finance assistant! ready to serve")
	})

	// Get User Language by Header
	app.Use(func(c *fiber.Ctx) error {
		allowLang := c.AcceptsLanguages("id", "en")
		lang := cast.ToString(c.Request().Header.Peek("Accept-Language"))
		if allowLang != "" {
			if lang != "" {
				fmt.Printf("load lang %s\n", lang)
				validation.Language = lang
			}
		} else {
			fmt.Printf("lang %s not found\n", lang)
		}

		return c.Next()
	})

	authHttp.NewHttpAuthHandler(app, authUseCase)
	journalHttp.NewHttpJournalHandler(app, journal)
	pubsub.NewPubSubSubscriber(journal).RunSubscription()

	bukuBesarHttp.NewHttpBukuBesarHandler(app, bukuBesarUseCase.NewBukuBesarUseCase(bukuBesarRepoMySql.NewMysqlBukuBesarRepository(dbConn)))

	//decode authorization from api gateway
	//make request auth: curl --location --request POST 'https://apis.uniqdev.tech/auth/v1/login' --header 'Content-Type: application/x-www-form-urlencoded' --data-urlencode 'email=<EMAIL>' --data-urlencode 'password=password'
	app.Use(config.AuthMiddleware)

	defer fmt.Println("service ended...")
	errs := make(chan error)

	go func() {
		c := make(chan os.Signal, 1)
		signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
		errs <- fmt.Errorf("%s", <-c)
	}()

	go func() {
		port := "3000"
		if os.Getenv("PORT") != "" {
			port = os.Getenv("PORT")
		}
		errs <- app.Listen(":" + port)
	}()

	//start cronjob
	cron.StartAsync()

	fmt.Printf("exit: %v", <-errs)
}
