package domain

type Outlet struct {
	OutletID           int    `json:"outlet_id"`
	Name               string `json:"name"`
	Address            string `json:"address"`
	Phone              string `json:"phone"`
	OutletLogo         string `json:"outlet_logo"`
	Country            string `json:"country"`
	Province           string `json:"province"`
	City               string `json:"city"`
	PostalCode         string `json:"postal_code"`
	Feature            string `json:"feature"`
	ReceiptNote        string `json:"receipt_note"`
	ReceiptPhone       string `json:"receipt_phone"`
	ReceiptAddress     string `json:"receipt_address"`
	ReceiptSocialmedia string `json:"receipt_socialmedia"`
	ReceiptLogo        string `json:"receipt_logo"`
	PaymentCash        int    `json:"payment_cash"`
	PaymentCard        int    `json:"payment_card"`
	PaymentCompliment  int    `json:"payment_compliment"`
	PaymentDuty        int    `json:"payment_duty"`
	PaymentPiutang     int    `json:"payment_piutang"`
	ExpiredDate        string `json:"expired_date"`
	AdminFkid          int    `json:"admin_fkid"`
	DataCreated        string `json:"data_created"`
	DataModified       string `json:"data_modified"`
	DataStatus         string `json:"data_status"`
	Latitude           int    `json:"latitude"`
	Longitude          int    `json:"longitude"`
	AppShow            int    `json:"app_show"`
}

type OutletAndAdmin struct {
	Outlet
	Admin
}
