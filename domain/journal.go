package domain

const (
	TransTypeSales       = 1
	TransTypeProduction  = 3
	TransTypeSalesRefund = 23
)

type Journal struct {
	TransType           int     `json:"trans_type,omitempty"`
	TransId             string  `json:"trans_id,omitempty"`
	TransCreated        int64   `json:"trans_created,omitempty"`
	AccountCode         string  `json:"account_code,omitempty"`
	AccountName         string  `json:"account_name,omitempty"`
	Type                string  `json:"type,omitempty"` //D or K
	Nominal             float64 `json:"nominal,omitempty"`
	AdminFkid           int     `json:"admin_fkid,omitempty"`
	OutletFkid          int     `json:"outlet_fkid,omitempty"`
	AccountCategoryFkid int     `json:"account_category_fkid,omitempty"`
	Description         string  `json:"description,omitempty"`
}

type Account struct {
	Name string
	Code string
}

type JournalTransaction struct {
	AdminId             int
	OutletId            int
	TransactionType     int //1: sales, 2: purchase, 3: production
	TransactionId       interface{}
	TransactionTime     int64
	DiscountTotal       int //all discount (even tax with category discount)
	TaxTotal            int //only gratuity with category: tax or service
	Item                []TransactionItem
	Payment             []TransactionPayment
	TransactionStatus   string
	DiscountTransaction int //discount per transaction
	PiutangCredit       int //pembayaran/pelunasan piutang
	TaxDetail           []TaxWithGratuity
	Description         string
}

type TransactionTax struct {
	Name  string
	Total int
}

type TransactionItem struct {
	PriceSell       int
	PriceBuy        float64
	Subtotal        int // price x qty - discount
	ProductId       int
	ProductDetailId int
	Discount        int //discount per item only
	Product         Product
	IsBreakdown     bool //true if item is breakdown
}

type TransactionPayment struct {
	Total    int
	Method   string //cash, card, piutang, compliment, duty_meals
	BankName string //only if method is card
	BankId   int    //only if method is card
}

type CoaJournal struct {
	NamaAkun            string `json:"nama_akun"`
	KodeAkun            string `json:"kode_akun"`
	Bertambah           string `json:"bertambah"`
	Berkurang           string `json:"berkurang"`
	AccountCategoryFkid string `json:"account_category_fkid"`
}

type AccountSetting struct {
	ProductFkid         int    `json:"product_fkid,omitempty"`
	AccountDebit        string `json:"account_debit,omitempty"`
	DebitName           string `json:"debit_name,omitempty"`
	AccountKredit       string `json:"account_kredit,omitempty"`
	CreditName          string `json:"credit_name,omitempty"`
	CreditCode          string `json:"credit_code,omitempty"`
	InventoryCode       string `json:"inventory_code,omitempty"`
	InventoryName       string `json:"inventory_name,omitempty"`
	DebitCategoryId     int    `json:"debit_category_id,omitempty"`
	CreditCategoryId    int    `json:"credit_category_id,omitempty"`
	InventoryCategoryId int    `json:"inventory_category_id,omitempty"`
	Expenses            int    `json:"expenses,omitempty"`
}

type JournalFailures struct {
	ID        int    `json:"id"`
	TransType int    `json:"trans_type"`
	TransID   string `json:"trans_id"`
	Info      string `json:"info"`
	CreatedAt int64  `json:"created_at"`
}

type DataPurchase struct {
	Id            int    `json:"purchase_id"`
	SubTotal      int    `json:"sub_total"`
	GrandTotal    int    `json:"grand_total"`
	Payment       string `json:"pay_type"`
	Amount        int    `json:"bayar"`
	DiscountTotal int    `json:"discount_total"`
	DiscountType  string `json:"type_discount"`
	Detail        []PurchaseDetail
}

type PurchaseDetail struct {
	PurchaseId        int    `json:"purchase_fkid"`
	PurchaseProductId int    `json:"purchase_products_id"`
	ProductDetailId   int    `json:"product_fkid"`
	ProductType       string `json:"catalogue_type"`
	QtyPurchase       int    `json:"qty_purchase"`
	QtyArive          int    `json:"qty_arive"`
	PriceBuy          int    `json:"price_nota"`
	Total             int    `json:"total"` // qty * price + markup - discount
}

type TransactionData struct {
	TransID string `db:"trans_id"`
	Type    string `db:"type"`
}

type MissingJournalParam struct {
	StartTime int64
	EndTime   int64
	AdminId   int
}
