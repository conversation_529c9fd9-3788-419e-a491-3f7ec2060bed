package domain

type Sales struct {
	SalesID         string      `json:"sales_id"`
	Display<PERSON>ota     string      `json:"display_nota,omitempty"`
	Payment         string      `json:"payment,omitempty"`
	DiningTable     string      `json:"dining_table,omitempty"`
	CustomerName    string      `json:"customer_name,omitempty"`
	MemberFkid      interface{} `json:"member_fkid,omitempty"`
	QtyCustomers    int         `json:"qty_customers,omitempty"`
	DataStatus      string      `json:"data_status,omitempty"`
	EmployeeFkid    int         `json:"employee_fkid,omitempty"`
	OutletFkid      int         `json:"outlet_fkid,omitempty"`
	Status          string      `json:"status,omitempty"`
	TimePrediction  int         `json:"time_prediction,omitempty"`
	OpenShiftFkid   int         `json:"open_shift_fkid,omitempty"`
	DateCreated     string      `json:"date_created,omitempty"`
	DateModified    string      `json:"date_modified,omitempty"`
	TimeCreated     int64       `json:"time_created,omitempty"`
	TimeModified    int64       `json:"time_modified,omitempty"`
	Discount        int         `json:"discount,omitempty"`
	DiscountInfo    string      `json:"discount_info,omitempty"`
	Voucher         int         `json:"voucher,omitempty"`
	VoucherInfo     string      `json:"voucher_info,omitempty"`
	GrandTotal      int         `json:"grand_total,omitempty"`
	ReceiptReceiver string      `json:"receipt_receiver,omitempty"`
	PointEarned     int         `json:"point_earned,omitempty"`
	SalesDetail     []SalesDetail
}

type SalesDetail struct {
	SalesDetailID      int     `json:"sales_detail_id"`
	SalesFkid          string  `json:"sales_fkid"`
	ProductFkid        int     `json:"product_fkid"`
	ProductDetailFkid  int     `json:"product_detail_fkid"`
	Qty                int     `json:"qty"`
	Price              int     `json:"price"`
	PriceAdd           int     `json:"price_add"`
	SubTotal           int     `json:"sub_total"`
	Discount           int     `json:"discount"`
	DiscountInfo       string  `json:"discount_info"`
	Parent             int     `json:"parent"`
	ChildType          string  `json:"child_type"`
	EmployeeFkid       int     `json:"employee_fkid"`
	Note               string  `json:"note"`
	TimeCreated        int64   `json:"time_created"`
	PriceBuy           float64 `json:"price_buy"`
	CommissionStaff    int     `json:"commission_staff"`
	CommissionCustomer int     `json:"commission_customer"`
}

type Payment struct {
	PaymentID   int    `json:"payment_id"`
	SalesFkid   string `json:"sales_fkid"`
	Method      string `json:"method,omitempty"`
	Total       int    `json:"total,omitempty"`
	Pay         int    `json:"pay,omitempty"`
	Info        string `json:"info,omitempty"`
	TimeCreated int64  `json:"time_created,omitempty"`
	BankName    string `json:"bank_name"`
	BankId      int
}

type Tax struct {
	SalesTaxID int    `json:"sales_tax_id"`
	TaxFkid    int    `json:"tax_fkid"`
	Total      int    `json:"total"`
	SalesFkid  string `json:"sales_fkid"`
	Category   string `json:"category"`
}

type Gratuity struct {
	GratuityID   int    `json:"gratuity_id"`
	Name         string `json:"name"`
	TaxCategory  string `json:"tax_category"`
	TaxStatus    string `json:"tax_status"`
	TaxType      string `json:"tax_type"`
	Jumlah       int    `json:"jumlah"`
	AdminFkid    int    `json:"admin_fkid"`
	DataCreated  int64  `json:"data_created"`
	DataModified int64  `json:"data_modified"`
	DataStatus   string `json:"data_status"`
}

type Promotion struct {
	SalesPromotionID int         `json:"sales_promotion_id"`
	SalesFkid        string      `json:"sales_fkid"`
	PromotionFkid    int         `json:"promotion_fkid"`
	PromotionBuyFkid int         `json:"promotion_buy_fkid"`
	VoucherCode      interface{} `json:"voucher_code"`
	PromotionValue   int         `json:"promotion_value"`
}

type SalesBreakdown struct {
	SalesBreakdownId  int     `json:"sales_breakdown_id"`
	SalesDetailFkid   int     `json:"sales_detail_fkid"`
	SalesVoidFkid     int     `json:"sales_void_fkid"`
	ProductDetailFkid int     `json:"product_detail_fkid"`
	QtyTotal          float64 `json:"qty_total"`
	PriceBuy          float32 `json:"price_buy"`
	Status            string  `json:"status"`
	SalesStatus       string  `json:"sales_status"`
	TimeCreated       int64   `json:"time_created"`
	TimeModified      int64   `json:"time_modified"`
	ProductFkid       int     `json:"product_fkid"`
}

type SalesRefund struct {
	SalesRefundID int    `json:"sales_refund_id,omitempty"`
	SalesFkid     string `json:"sales_fkid,omitempty"`
	Reason        string `json:"reason,omitempty"`
	TimeCreated   int64  `json:"time_created,omitempty"`
	TimeModified  int64  `json:"time_modified,omitempty"`
	GrandTotal    int    `json:"grand_total,omitempty"`
	EmployeeFkid  int    `json:"employee_fkid,omitempty"`
}

type SalesWithPayment struct {
	Sales
	Payment
}

type TaxWithGratuity struct {
	Tax
	Gratuity
}

type SalesUseCase interface {
	GetProduct(id int) []Sales
	GetSales()
}

type SalesRepository interface {
	GetSales()
}
