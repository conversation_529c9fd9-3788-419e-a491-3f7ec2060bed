package domain

type BukuBesar struct {
	AccountCode string
	AccountName string
	Debit       int
	Kredit      int
	Total       int
}

type Param struct {
	StartDate string `json:"startDate" validate:"required"`
	EndDate   string `json:"endDate" validate:"required"`
	Outlet    string `json:"outlet" validate:"required"`
	AdminFkid string `json:"adminFkid" validate:"required"`
}

type Result struct {
	Status      string
	TotalRecord int
	Data        []BukuBesar
}

type ParamDetail struct {
	AdminFkid   string `json:"adminFkid" validate:"required"`
	StartDate   string `json:"startDate" validate:"required"`
	EndDate     string `json:"endDate" validate:"required"`
	Outlet      string `json:"outlet" validate:"required"`
	AccountCode string `json:"accountCode" validate:"required"`
}

type Detail struct {
	Date        string
	TransType   string
	AccountType string
	Des         string
	Debit       int
	Kredit      int
	Total       int
}

type ResultDetail struct {
	Status      string
	TotalRecord int
	Data        []Detail
}

type BukuBesarUseCase interface {
	GetBukuBesar(Param) []BukuBesar
	DetailBukuBesar(ParamDetail) []Detail
}
type BukuBesarRepository interface {
	DataBukuBesar(Param) []map[string]interface{}
	DataDetailBukuBesar(ParamDetail) []map[string]interface{}
	GetSaldo(ParamDetail) []map[string]interface{}
}
