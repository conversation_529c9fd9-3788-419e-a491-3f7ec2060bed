package domain

type Product struct {
	ProductId       int    `json:"product_id,omitempty"`
	Name            string `json:"name,omitempty"`
	Barcode         string `json:"barcode,omitempty"`
	StockManagement int    `json:"stock_management,omitempty"`
}

type ProductContract interface {
	Fetch() ([]Product, error)
	FetchById(id int64) (Product, error)
}

type ProductUseCase interface {
	ProductContract
}

type ProductRepository interface {
	ProductContract
	Add(Product) error
}
