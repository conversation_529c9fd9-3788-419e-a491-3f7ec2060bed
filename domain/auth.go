package domain

import "gitlab.com/uniqdev/backend/service-finance/core/model"

type Auth struct{}
type AuthUseCase interface{
	CreateToken(AuthInputLogin) (UserToken, error, model.Response)
}
type AuthRepository interface{
	FindAccount(string, string) (User, string, error)
}

type AuthInputLogin struct {
	Email    string `json:"email" validate:"required" xml:"email" form:"email"`
	Password string `json:"password" validate:"required" xml:"password" form:"password"`
}