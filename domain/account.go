package domain

const (
	AccountTypeGenera   string = "general"
	AccountTypePayment  string = "payment"
	AccountTypeGratuity string = "gratuity"
)

type AccountDefault struct {
	Code                string `json:"code,omitempty"`
	Name                string `json:"name,omitempty"`
	IdxKey              string `json:"idx_key,omitempty"`
	AccountCategoryFkid int    `json:"account_category_fkid,omitempty"`
	Type                string `json:"type,omitempty"`
	TypeFkid            int    `json:"type_fkid,omitempty"`
}

const (
	KeySalesPendapatan      = "sales_pendapatan"
	KeySalesDiskon          = "sales_diskon"
	KeySalesCash            = "sales_cash"
	KeySalesBank            = "sales_bank"
	KeySalesTax             = "sales_tax"
	KeySalesPenjualan       = "sales_penjualan"
	KeyPersediaanPersediaan = "persediaan_persediaan"
	KeyPurhcaseCogs         = "purchase_cogs"
	KeyPiutangUsaha         = "piutang_usaha"
)
