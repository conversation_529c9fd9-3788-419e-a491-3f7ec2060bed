package domain

type User struct {
	Name       string `json:"name"`
	Email      string `json:"email"`
	Phone      string `json:"phone"`
	UserType   string `json:"user_type"`
	UserId     string `json:"user_id"`
	BusinessId string `json:"business_id"`
	AccountId  string `json:"account_id"`
}

type Admin struct {
	AdminID          int    `json:"admin_id"`
	BusinessName     string `json:"business_name"`
	Name             string `json:"name"`
	Email            string `json:"email"`
	Password         string `json:"password"`
	ActivationStatus string `json:"activation_status"`
	DateJoin         int64  `json:"date_join"`
	DeveloperMode    int    `json:"developer_mode"`
	DataCreated      int64  `json:"data_created"`
	DataModified     int64  `json:"data_modified"`
	Phone            string `json:"phone"`
	Photo            string `json:"photo"`
	LastLogin        int64  `json:"last_login"`
	Settings         string `json:"settings"`
	BusinessType     string `json:"business_type"`
	AccountID        string `json:"account_id"`
	RegisterRefCode  string `json:"register_ref_code"`
}

type UserRole struct {
	OutletAccess string `json:"outlet_access"`
}
