package mysql

import (
	"database/sql"

	mysql "gitlab.com/uniqdev/backend/service-finance/core/mysql"
	"gitlab.com/uniqdev/backend/service-finance/core/util/array"
	"gitlab.com/uniqdev/backend/service-finance/model"
	"gitlab.com/uniqdev/backend/service-finance/module/account"
)

type accountRepository struct {
	db mysql.Repository
}

func NewMysqlAccountRepository(db *sql.DB) account.Repository {
	return &accountRepository{mysql.Repository{Conn: db}}
}

func (a accountRepository) FetchFinanceAccount(adminId int) ([]model.Account, error) {
	var result []model.Account
	err := a.db.Query("select * from finance_accounts where admin_fkid = ?", adminId).Model(&result)
	return result, err
}

func (a accountRepository) CreateFinanceAccount(accont model.Account) (int64, error) {
	accountMap := accont.ToMap()
	accountMap["account_id"] = nil
	array.RemoveEmptyKeys(accountMap)

	resp, err := a.db.Insert("finance_accounts", accountMap)
	if err != nil {
		return 0, err
	}

	id, _ := resp.LastInsertId()
	return id, err
}

func (a accountRepository) FetchAccountGeneral(adminId int) ([]model.AccountGeneral, error) {
	var result []model.AccountGeneral
	sql := `select * from finance_account_general where admin_fkid = ?`
	err := a.db.Query(sql, adminId).Model(&result)
	return result, err
}

func (a accountRepository) CreateAccountGeneral(accountGeneral model.AccountGeneral) (int64, error) {
	accountGeneralMap := accountGeneral.ToMap()
	accountGeneralMap["id"] = nil
	array.RemoveEmptyKeys(accountGeneralMap)

	resp, err := a.db.Insert("finance_account_general", accountGeneralMap)
	if err != nil {
		return 0, err
	}

	id, _ := resp.LastInsertId()
	return id, err
}
