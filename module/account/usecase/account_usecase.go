package usecase

import (
	"fmt"
	"time"

	"gitlab.com/uniqdev/backend/service-finance/core/log"
	"gitlab.com/uniqdev/backend/service-finance/module/account"
)

type accountUseCase struct {
	repo account.Repository
}

func NewAccountUseCase(repository account.Repository) account.Usecase {
	return &accountUseCase{repository}
}

func (a accountUseCase) CreateDefaultAccount(adminId int) error {
	baseAdminId := 1 //base admin-id to copy
	if adminId == baseAdminId {
		fmt.Println("its a base adminId...")
		return nil
	}

	financeAccountsBase, err := a.repo.FetchFinanceAccount(baseAdminId)
	if log.IfError(err) {
		return err
	}

	idMap := make(map[int]int64)
	for _, financeAccount := range financeAccountsBase {
		financeAccount.AdminFkid = adminId
		financeAccount.CreatedAt = time.Now().Unix() * 1000
		financeAccount.UpdatedAt = time.Now().Unix() * 1000
		id, err := a.repo.CreateFinanceAccount(financeAccount)
		if log.IfError(err) {
			return err
		}
		idMap[financeAccount.AccountID] = id
	}

	accountGeneralBase, err := a.repo.FetchAccountGeneral(baseAdminId)
	if log.IfError(err) {
		return err
	}

	for _, accountGeneral := range accountGeneralBase {
		accountGeneral.AccountFkid = int(idMap[accountGeneral.AccountFkid])
		accountGeneral.AdminFkid = adminId

		_, err := a.repo.CreateAccountGeneral(accountGeneral)
		if log.IfError(err) {
			return err
		}
	}

	log.Info("finish creating account finance of: %v | base took from: %v", adminId, baseAdminId)
	return nil
}
