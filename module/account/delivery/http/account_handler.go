package http

import (
	v2 "github.com/gofiber/fiber/v2"
	domain "gitlab.com/uniqdev/backend/service-finance/domain"
)

type accountHandler struct {
	domain.AccountUseCase
}

func NewHttpAccountHandler(app *v2.App, useCase domain.AccountUseCase) {
	handler := &accountHandler{useCase}
	app.Get("/module/account", handler.Sample)
}
func (h accountHandler) Sample(c *v2.Ctx) error {
	return c.SendString("this is sample of account feature route")
}
