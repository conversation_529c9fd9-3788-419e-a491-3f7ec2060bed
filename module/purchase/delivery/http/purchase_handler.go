package http

import (
	v2 "github.com/gofiber/fiber/v2"
	domain "gitlab.com/uniqdev/backend/service-finance/domain"
)

type purchaseHandler struct {
	domain.PurchaseUseCase
}

func NewHttpPurchaseHandler(app *v2.App, useCase domain.PurchaseUseCase) {
	handler := &purchaseHandler{useCase}
	app.Get("/module/purchase", handler.Sample)
}
func (h purchaseHandler) Sample(c *v2.Ctx) error {
	return c.SendString("this is sample of purchase feature route")
}
