package http

import (
	v2 "github.com/gofiber/fiber/v2"
	"gitlab.com/uniqdev/backend/service-finance/core/model"
	"gitlab.com/uniqdev/backend/service-finance/core/util/validation"
	domain "gitlab.com/uniqdev/backend/service-finance/domain"
)

type authHandler struct {
	domain.AuthUseCase
}

func NewHttpAuthHandler(app *v2.App, useCase domain.AuthUseCase) {
	handler := &authHandler{useCase}
	app.Post("/auth/login", handler.Login)
}

func (h authHandler) Login(c *v2.Ctx) error {
	var inputLogin domain.AuthInputLogin
	c.BodyParser(&inputLogin)
	//if validation.IsJson(c.Body()) {
	//	cast.JsonToStruct(c.Body(), &inputLogin)
	//}else{
	//	//get input
	//	inputLogin = domain.Login{
	//		Email:    c.<PERSON>alue("email"),
	//		Password: c.FormValue("password"),
	//	}
	//}

	//validate input
	errList := validation.Struct(inputLogin)
	if validation.IsError(errList) {
		c.Status(400)
		return c.JSON(model.Response{
			Code:    400,
			Message: "Invalid input",
			Data:    errList,
		})
	}

	// process create token
	user, err, errData := h.AuthUseCase.CreateToken(inputLogin)
	if err != nil {
		c.Status(errData.Code)
		return c.JSON(errData)
	}

	return c.JSON(model.Response{
		Code:    200,
		Message: "ok",
		Data:    user,
	})
}
