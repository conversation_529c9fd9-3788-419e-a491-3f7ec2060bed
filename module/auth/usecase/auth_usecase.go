package usecase

import (
	"errors"
	"github.com/dgrijalva/jwt-go"
	"gitlab.com/uniqdev/backend/service-finance/core/model"
	"gitlab.com/uniqdev/backend/service-finance/domain"
	"golang.org/x/crypto/bcrypt"
	"time"
)

type authUseCase struct {
	domain.AuthRepository
}

func NewAuthUseCase(repository domain.AuthRepository) domain.AuthUseCase {
	return &authUseCase{repository}
}

func (u authUseCase) CreateToken(lg domain.AuthInputLogin) (domain.UserToken, error, model.Response) {
	var userToken domain.UserToken

	//1. check user from DB
	//-- check user is valid
	user, pass, err := u.AuthRepository.FindAccount("admin", lg.Email)
	if user.UserId == "" {
		user, pass, err = u.AuthRepository.FindAccount("employee", lg.Email)
	}
	if user.UserId == "" {
		return userToken, errors.New("account not found"), model.Response{
			Code:    404,
			Message: "Account not found",
			Data:    nil,
		}
	}

	//-- validate user's password
	err = bcrypt.CompareHashAndPassword([]byte(pass), []byte(lg.Password))
	if err != nil {
		return userToken, errors.New("invalid email or password"), model.Response{
			Code:    400,
			Message: "Invalid email or password",
			Data:    nil,
		}
	}

	//3. if valid, then create token
	token := jwt.New(jwt.SigningMethodHS256)
	expired := time.Now().Add(time.Hour * 72).Unix()
	claims := token.Claims.(jwt.MapClaims)
	claims["exp"] = expired
	claims["role"] = "{}"
	claims["data"] = user

	// Generate encoded token and send it as response.
	generatedToken, err := token.SignedString([]byte("secret"))

	//prepare output
	userToken.User = user
	userToken.Token = domain.Token{
		Token:   generatedToken,
		Expired: expired,
		Type:    "Bearer",
	}

	return userToken, err, model.Response{}
}
