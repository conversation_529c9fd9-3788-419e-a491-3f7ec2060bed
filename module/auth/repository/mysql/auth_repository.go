package mysql

import (
	"database/sql"
	mysql "gitlab.com/uniqdev/backend/service-finance/core/mysql"
	"gitlab.com/uniqdev/backend/service-finance/core/util/cast"
	domain "gitlab.com/uniqdev/backend/service-finance/domain"
)

type authRepository struct {
	mysql.Repository
}

func NewMysqlAuthRepository(db *sql.DB) domain.AuthRepository {
	return &authRepository{mysql.Repository{Conn: db}}
}

func (r authRepository) FindAccount(userType, email string) (domain.User, string, error) {
	var user domain.User
	var password string
	var err error
	var result map[string]interface{}

	if userType == "admin" {
		result, err = r.Query(`
		SELECT email, password, phone, name,
			admin_id AS user_id,
			admin_id AS business_id,
			'admin' AS user_type,
			'' AS account_id
		FROM admin
		WHERE email=? AND activation_status=?`, email, "activated").Map()
	}
	if userType == "employee" {
		result, err = r.Query(`
		SELECT email, password, phone, name,
			employee_id AS user_id,
			admin_fkid AS business_id,
			'employee' AS user_type,
			'' AS account_id
		FROM employee
		WHERE email=? AND access_status_web=?`, email, "activated").Map()
	}

	password = cast.ToString(result["password"])
	cast.MapToStruct(result, &user)
	return user, password, err
}