package http

import (
	"fmt"
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	v2 "github.com/gofiber/fiber/v2"
	domain "gitlab.com/uniqdev/backend/service-finance/domain"
)

type bukuBesarHandler struct {
	domain.BukuBesarUseCase
}

func NewHttpBukuBesarHandler(app *v2.App, useCase domain.BukuBesarUseCase) {
	handler := &bukuBesarHandler{useCase}
	app.Post("/buku_besar", handler.dataBukuBesar)
	app.Post("/buku_besar/detail", handler.getDetailBukuBesar)
}

// type ErrorResponse struct {
// 	FailedField string
// 	Tag         string
// 	Value       string
// }

func ValidateStruct(param interface{}) interface{} {
	type response struct {
		Field string
		Msg   string
	}
	var errors []*response
	var validate = validator.New()
	err := validate.Struct(param)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			var element response
			element.Field = err.Field()
			element.Msg = err.Tag() + err.Param()
			errors = append(errors, &element)
		}
	}
	fmt.Println(errors)
	if len(errors) == 0 {
		return nil
	} else {
		return errors
	}
}

func (h bukuBesarHandler) Sample(c *v2.Ctx) error {
	return c.SendString("this is sample of bukuBesar feature route")
}

func (h bukuBesarHandler) dataBukuBesar(c *fiber.Ctx) error {

	post := new(domain.Param)
	if err := c.BodyParser(post); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"message": err.Error(),
		})

	}
	errors := ValidateStruct(*post)
	if errors != nil {
		return c.Status(fiber.StatusBadRequest).JSON(errors)
	}
	//Do something else here
	result := h.GetBukuBesar(*post)
	//Return user
	var res domain.Result
	res.Data = result
	res.Status = "success"
	res.TotalRecord = len(result)
	return c.JSON(res)
}

func (h bukuBesarHandler) getDetailBukuBesar(c *fiber.Ctx) error {
	post := new(domain.ParamDetail)
	if err := c.BodyParser(post); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"message": err.Error(),
		})
	}
	errors := ValidateStruct(*post)
	if errors != nil {
		return c.Status(fiber.StatusBadRequest).JSON(errors)
	}
	//Do something else here
	result := h.DetailBukuBesar(*post)
	//Return user
	var res domain.ResultDetail
	res.Data = result
	res.Status = "success"
	res.TotalRecord = len(result)
	return c.JSON(res)
}
