package usecase

import (
	"gitlab.com/uniqdev/backend/service-finance/core/util/cast"
	domain "gitlab.com/uniqdev/backend/service-finance/domain"
)

type bukuBesarUseCase struct {
	domain.BukuBesarRepository
}

func NewBukuBesarUseCase(repository domain.BukuBesarRepository) domain.BukuBesarUseCase {
	return &bukuBesarUseCase{repository}
}

func (s bukuBesarUseCase) GetBukuBesar(param domain.Param) []domain.BukuBesar {
	data := s.DataBukuBesar(param)
	// fmt.Println(data)
	var drawData []domain.BukuBesar
	var result []domain.BukuBesar
	for _, a := range data {
		var tmp domain.BukuBesar
		tmp.AccountCode = cast.ToString(a["account_code"])
		tmp.AccountName = cast.ToString(a["account_name"])
		tmp.Debit = cast.ToInt(a["total_debit"])
		tmp.Kredit = cast.ToInt(a["total_kredit"])
		total := 0
		if a["type_category"] == "D" {
			total = cast.ToInt(a["total_debit"]) - cast.ToInt(a["total_kredit"])
		} else {
			total = cast.ToInt(a["total_kredit"]) - cast.ToInt(a["total_debit"])
		}
		tmp.Total = total
		drawData = append(drawData, tmp)
	}
	result = drawData
	return result
}

func (s bukuBesarUseCase) DetailBukuBesar(param domain.ParamDetail) []domain.Detail {
	var result []domain.Detail
	data := s.DataDetailBukuBesar(param)
	dataSaldo := s.GetSaldo(param)
	if len(dataSaldo) > 0 {
		var tmpSaldo domain.Detail
		tmpSaldo.Date = cast.ToString(dataSaldo[0]["date"])
		tmpSaldo.TransType = "Saldo Awal"
		tmpSaldo.Debit = 0
		tmpSaldo.Kredit = 0
		tmpSaldo.Des = ""
		tmpSaldo.AccountType = ""
		tmpSaldo.Total = cast.ToInt(dataSaldo[0]["saldo"])
		result = append(result, tmpSaldo)
	}

	for _, v := range data {
		var tmp domain.Detail
		tmp.Date = cast.ToString(v["date"])
		tmp.TransType = cast.ToString(v["trans_type"])
		tmp.Debit = cast.ToInt(v["total_debit"])
		tmp.Kredit = cast.ToInt(v["total_kredit"])
		tmp.Des = cast.ToString(v["description"])
		tmp.AccountType = cast.ToString(v["type_category"])
		total := 0
		if v["type_category"] == "D" {
			total = cast.ToInt(v["total_debit"]) - cast.ToInt(v["total_kredit"])
		} else {
			total = cast.ToInt(v["total_kredit"]) - cast.ToInt(v["total_debit"])
		}
		tmp.Total = total
		result = append(result, tmp)
	}

	return result
}
