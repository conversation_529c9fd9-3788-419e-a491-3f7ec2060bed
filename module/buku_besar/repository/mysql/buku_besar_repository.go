package mysql

import (
	"database/sql"

	"gitlab.com/uniqdev/backend/service-finance/core/log"
	mysql "gitlab.com/uniqdev/backend/service-finance/core/mysql"
	domain "gitlab.com/uniqdev/backend/service-finance/domain"
)

type bukuBesarRepository struct {
	db mysql.Repository
}

func NewMysqlBukuBesarRepository(db *sql.DB) domain.BukuBesarRepository {
	return &bukuBesarRepository{mysql.Repository{Conn: db}}
}

func (b bukuBesarRepository) DataBukuBesar(param domain.Param) []map[string]interface{} {
	sql := `select
		fju.account_code,
		min(fju.account_name) as account_name,
		ifnull(sum((select fju.nominal where fju.type="D")),0) as total_debit,
		ifnull(sum((select fju.nominal where fju.type="K")),0) as total_kredit,
		min(fac.type) as type_category
		from finance_jurnal_umum fju
		left join finance_accounts_category fac on fac.account_category_id=fju.account_category_fkid
		where fju.trans_created between ? and ?
		and fju.outlet_fkid in ("` + param.Outlet + `")
		group by fju.account_code`

	result, err := b.db.Query(sql, param.StartDate, param.EndDate).MapArray()
	log.IfError(err)
	// resultSql, err := b.db.Query(sql, param.StartDate, param.EndDate, param.Outlet).PrintSql().MapArray()
	// fmt.Println(resultSql)
	return result
}

func (b bukuBesarRepository) DataDetailBukuBesar(param domain.ParamDetail) []map[string]interface{} {
	sql := `select 
	fju.trans_created as date,
	fju.trans_type,
	fju.account_code,
	fju.account_name,
	fju.description,
	ifnull((select fju.nominal where fju.type="D"),0) as total_debit,
	ifnull((select fju.nominal where fju.type="K"),0) as total_kredit,
	fac.type as type_category
	from finance_jurnal_umum fju
	left join finance_accounts_category fac on fac.account_category_id=fju.account_category_fkid
	where fju.account_code = ?
	and fju.admin_fkid = ?
	and fju.outlet_fkid in ("` + param.Outlet + `")
	and fju.trans_created between ? and ?
	order by fju.trans_created asc`

	result, err := b.db.Query(sql, param.AccountCode, param.AdminFkid, param.StartDate, param.EndDate).MapArray()
	log.IfError(err)
	// resultSql, err := b.db.Query(sql, param.AccountCode, param.AdminFkid, param.Outlet, param.StartDate, param.EndDate).PrintSql().MapArray()
	// fmt.Println(resultSql)
	return result
}

func (b bukuBesarRepository) GetSaldo(param domain.ParamDetail) []map[string]interface{} {
	sql := `select saldo,date from finance_account_saldo where admin_fkid = ? and account_code = ? order by date desc limit 1`
	result, err := b.db.Query(sql, param.AdminFkid, param.AccountCode).MapArray()
	log.IfError(err)
	// resultSql, err := b.db.Query(sql, param.AccountCode, param.AdminFkid, param.Outlet, param.StartDate, param.EndDate).PrintSql().MapArray()
	// fmt.Println(resultSql)
	return result
}
