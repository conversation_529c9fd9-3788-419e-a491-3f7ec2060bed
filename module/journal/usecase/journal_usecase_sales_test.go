package usecase

import (
	"encoding/json"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/uniqdev/backend/service-finance/core/util/cast"
	domain "gitlab.com/uniqdev/backend/service-finance/domain"
	"gitlab.com/uniqdev/backend/service-finance/module/journal"
	journalMock "gitlab.com/uniqdev/backend/service-finance/module/journal/mocks"
	productMock "gitlab.com/uniqdev/backend/service-finance/module/product/mocks"
)

func Test_journalUseCase_addToJournalMockAccountSetting(t *testing.T) {
	//sales
	salesCard := `{"AdminId":1,"OutletId":364,"TransactionType":1,"TransactionId":"INV-ONS2023012414","TransactionTime":*************,"DiscountTotal":0,"TaxTotal":0,"Item":[{"PriceSell":12000,"PriceBuy":0,"Subtotal":12000,"ProductId":4975,"ProductDetailId":11249,"Discount":0,"Product":{"product_id":4975,"name":"Ayam Geprek Paha"}},{"PriceSell":19000,"PriceBuy":0,"Subtotal":19000,"ProductId":5732,"ProductDetailId":12971,"Discount":0,"Product":{"product_id":5732,"name":"Kopi Masa Lalu"}},{"PriceSell":19000,"PriceBuy":0,"Subtotal":19000,"ProductId":5731,"ProductDetailId":12970,"Discount":0,"Product":{"product_id":5731,"name":"Kopi Mantan"}}],"Payment":[{"Total":50000,"Method":"card","BankName":"OVO","BankId":79}],"TransactionStatus":"success","DiscountTransaction":0,"PiutangCredit":0}`
	salesCardNoAccount := `{"AdminId":1,"OutletId":364,"TransactionType":1,"TransactionId":"INV-ONS2023012414","TransactionTime":*************,"DiscountTotal":0,"TaxTotal":0,"Item":[{"PriceSell":12000,"PriceBuy":0,"Subtotal":12000,"ProductId":4975,"ProductDetailId":11249,"Discount":0,"Product":{"product_id":4975,"name":"Ayam Geprek Paha"}},{"PriceSell":19000,"PriceBuy":0,"Subtotal":19000,"ProductId":5732,"ProductDetailId":12971,"Discount":0,"Product":{"product_id":5732,"name":"Kopi Masa Lalu"}},{"PriceSell":19000,"PriceBuy":0,"Subtotal":19000,"ProductId":5731,"ProductDetailId":12970,"Discount":0,"Product":{"product_id":5731,"name":"Kopi Mantan"}}],"Payment":[{"Total":50000,"Method":"card","BankName":"Gopay","BankId":30}],"TransactionStatus":"success","DiscountTransaction":0,"PiutangCredit":0}`
	salesTax := `{"AdminId":1,"OutletId":29,"TransactionType":1,"TransactionId":"1N7N0O46S3465","TransactionTime":*************,"DiscountTotal":0,"TaxTotal":3200,"Item":[{"PriceSell":8000,"PriceBuy":0,"Subtotal":16000,"ProductId":2485,"ProductDetailId":8394,"Discount":0,"Product":{"product_id":2485,"name":"Jagung Bakar Asin","stock_management":1}},{"PriceSell":8000,"PriceBuy":0,"Subtotal":16000,"ProductId":2481,"ProductDetailId":8392,"Discount":0,"Product":{"product_id":2481,"name":"Jagung Bakar Keju","stock_management":1}}],"Payment":[{"Total":35200,"Method":"cash","BankName":"","BankId":0}],"TransactionStatus":"success","DiscountTransaction":0,"PiutangCredit":0,"TaxDetail":[{"total":3200,"category":"tax","gratuity_id":113,"name":"Tax PB"}],"Description":"OM202302101"}`
	salesTaxNoAccount := `{"AdminId":1,"OutletId":29,"TransactionType":1,"TransactionId":"1N7N0O46S3465","TransactionTime":*************,"DiscountTotal":0,"TaxTotal":3200,"Item":[{"PriceSell":8000,"PriceBuy":0,"Subtotal":16000,"ProductId":2485,"ProductDetailId":8394,"Discount":0,"Product":{"product_id":2485,"name":"Jagung Bakar Asin","stock_management":1}},{"PriceSell":8000,"PriceBuy":0,"Subtotal":16000,"ProductId":2481,"ProductDetailId":8392,"Discount":0,"Product":{"product_id":2481,"name":"Jagung Bakar Keju","stock_management":1}}],"Payment":[{"Total":35200,"Method":"cash","BankName":"","BankId":0}],"TransactionStatus":"success","DiscountTransaction":0,"PiutangCredit":0,"TaxDetail":[{"total":3200,"category":"tax","gratuity_id":1,"name":"Tax PB"}],"Description":"OM202302101"}`
	salesProductWithAccount := `{"AdminId":1,"OutletId":364,"TransactionType":1,"TransactionId":"INV-ONS2023012414","TransactionTime":*************,"DiscountTotal":0,"TaxTotal":0,"Item":[{"PriceSell":12000,"PriceBuy":0,"Subtotal":12000,"ProductId":4975,"ProductDetailId":14993,"Discount":0,"Product":{"product_id":4975,"name":"Ayam Geprek Paha"}},{"PriceSell":19000,"PriceBuy":0,"Subtotal":19000,"ProductId":5732,"ProductDetailId":12971,"Discount":0,"Product":{"product_id":5732,"name":"Kopi Masa Lalu"}},{"PriceSell":19000,"PriceBuy":0,"Subtotal":19000,"ProductId":5731,"ProductDetailId":12970,"Discount":0,"Product":{"product_id":5731,"name":"Kopi Mantan"}}],"Payment":[{"Total":50000,"Method":"card","BankName":"OVO","BankId":79}],"TransactionStatus":"success","DiscountTransaction":0,"PiutangCredit":0}`

	//account setting
	accountDefaultJson := `[{"code":"4-40200","name":"Retur Penjualan","idx_key":"sles_retur","account_category_fkid":13,"type":"general","type_fkid":1},{"code":"2-20500","name":"PPN Keluaran","idx_key":"sales_tax","account_category_fkid":10,"type":"general","type_fkid":1},{"code":"1-10101","name":"Piutang Belum Ditagih","idx_key":"sales_piutang","account_category_fkid":1,"type":"general","type_fkid":1},{"code":"4-40201","name":"Pendapatan Belum Ditagih","idx_key":"sales_penjualan","account_category_fkid":13,"type":"general","type_fkid":1},{"code":"7-70099","name":"Pendapatan Lain - lain","idx_key":"sales_pengiriman","account_category_fkid":14,"type":"general","type_fkid":1},{"code":"4-40000","name":"Pendapatan","idx_key":"sales_pendapatan","account_category_fkid":13,"type":"general","type_fkid":1},{"code":"2-20203","name":"Pendapatan Diterima Di Muka","idx_key":"sales_pembayaran","account_category_fkid":10,"type":"general","type_fkid":1},{"code":"4-40100","name":"Diskon Penjualan","idx_key":"sales_diskon","account_category_fkid":13,"type":"general","type_fkid":1},{"code":"1-10001","name":"Kas","idx_key":"sales_cash","account_category_fkid":3,"type":"general","type_fkid":1},{"code":"1-10002","name":"Rekening Bank","idx_key":"sales_bank","account_category_fkid":3,"type":"general","type_fkid":1},{"code":"1-10500","name":"PPN Masukan","idx_key":"purchase_tax","account_category_fkid":2,"type":"general","type_fkid":2},{"code":"5-50300","name":"Pengiriman & Pengangkutan","idx_key":"purchase_pengiriman","account_category_fkid":15,"type":"general","type_fkid":2},{"code":"1-10403","name":"Uang Muka","idx_key":"purchase_payment","account_category_fkid":2,"type":"general","type_fkid":2},{"code":"8-80999","name":"Beban Lain - lain","idx_key":"purchase_operasional","account_category_fkid":17,"type":"general","type_fkid":2},{"code":"2-20100","name":"Hutang Usaha","idx_key":"purchase_hutang","account_category_fkid":8,"type":"general","type_fkid":2},{"code":"1-10100","name":"Piutang Usaha","idx_key":"purchase_debitmemo","account_category_fkid":1,"type":"general","type_fkid":2},{"code":"5-50000","name":"Beban Pokok Pendapatan","idx_key":"purchase_cogs","account_category_fkid":15,"type":"general","type_fkid":2},{"code":"1-10001","name":"Kas","idx_key":"purchase_cash","account_category_fkid":3,"type":"general","type_fkid":2},{"code":"1-10002","name":"Rekening Bank","idx_key":"purchase_bank","account_category_fkid":3,"type":"general","type_fkid":2},{"code":"6-60217","name":"Listrik","idx_key":"prc_category#6","account_category_fkid":16,"type":"prc_category","type_fkid":6},{"code":"6-60218","name":"Air","idx_key":"prc_category#5","account_category_fkid":16,"type":"prc_category","type_fkid":5},{"code":"6-60103","name":"Makanan & Transportasi","idx_key":"prc_category#4","account_category_fkid":16,"type":"prc_category","type_fkid":4},{"code":"1-10100","name":"Piutang Usaha","idx_key":"piutang_usaha","account_category_fkid":1,"type":"general","type_fkid":11},{"code":"8-80100","name":"Penyesuaian Persediaan","idx_key":"persediaan_umum","account_category_fkid":17,"type":"general","type_fkid":10},{"code":"6-60216","name":"Pengeluaran Barang Rusak","idx_key":"persediaan_rusak","account_category_fkid":16,"type":"general","type_fkid":10},{"code":"5-50500","name":"Biaya Produksi","idx_key":"persediaan_produksi","account_category_fkid":15,"type":"general","type_fkid":10},{"code":"1-10200","name":"Persediaan Barang","idx_key":"persediaan_persediaan","account_category_fkid":4,"type":"general","type_fkid":10},{"code":"7-70099","name":"Pendapatan Lain - lain","idx_key":"pendapatan_lain","account_category_fkid":14,"type":"general","type_fkid":12},{"code":"1-10003","name":"Kas di Mesin Kasir","idx_key":"payment#79","account_category_fkid":3,"type":"payment","type_fkid":79},{"code":"1-10002","name":"Rekening Bank","idx_key":"payment#35","account_category_fkid":3,"type":"payment","type_fkid":35},{"code":"3-30000","name":"Modal Saham","idx_key":"modal_saham","account_category_fkid":12,"type":"general","type_fkid":12},{"code":"2-20100","name":"Hutang Usaha","idx_key":"hutang_usaha","account_category_fkid":8,"type":"general","type_fkid":11},{"code":"4-40100","name":"Diskon Penjualan","idx_key":"gratuity#48","account_category_fkid":13,"type":"gratuity","type_fkid":48},{"code":"2-20599","name":"Hutang Pajak Lainnya","idx_key":"gratuity#113","account_category_fkid":10,"type":"gratuity","type_fkid":113},{"code":"3-30999","name":"Ekuitas Saldo Awal","idx_key":"ekuitas_saldo_awal","account_category_fkid":12,"type":"general","type_fkid":12},{"code":"1-10705","name":"Aset Tetap - Perlengkapan Kantor","idx_key":"aset_tetap","account_category_fkid":5,"type":"general","type_fkid":12}]`
	accountProductsJson := `[{"product_detail_fkid":14993,"account_debit":"5-50000","debit_name":"Beban Pokok Pendapatan","account_kredit":"4-40000","credit_name":"Pendapatan","inventory_code":"1-10200","inventory_name":"Persediaan Barang","debit_category_id":15,"credit_category_id":13,"inventory_category_id":4}]`

	var result []domain.AccountDefault
	err := json.Unmarshal([]byte(accountDefaultJson), &result)
	assert.NoError(t, err)

	var accountProduct []domain.AccountSetting
	err = json.Unmarshal([]byte(accountProductsJson), &accountProduct)
	assert.NoError(t, err)

	mockJournalRepo := new(journalMock.Repository)
	mockProductRepo := new(productMock.Repository)

	type args struct {
		transaction string
	}

	tests := []struct {
		name string
		args args
		want int
	}{
		{"salesCardWithAccountSetting", args{salesCard}, 1},
		{"salesCardNoAccount", args{salesCardNoAccount}, 1},
		{"salesTax", args{salesTax}, 1},
		{"salesTaxNoAccount", args{salesTaxNoAccount}, 1},
		{"salesProductWithAccount", args{salesProductWithAccount}, 1},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			var journalTransaction domain.JournalTransaction
			err = json.Unmarshal([]byte(tt.args.transaction), &journalTransaction)
			assert.NoError(t, err)

			// productDetailIds := make([]int, 0)
			// for _, item := range journalTransaction.Item {
			// 	productDetailIds = append(productDetailIds, item.ProductDetailId)
			// }

			mockJournalRepo.On("FetchAccountDefault", journalTransaction.AdminId).Return(result, nil)
			mockJournalRepo.On("GetAccountByProduct", mock.Anything).Return(accountProduct)
			mockJournalRepo.On("InsertJournals", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()
			j := journalUseCase{mockJournalRepo, mockProductRepo}
			journal := j.formatJournal(journalTransaction)
			fmt.Println(">>> ", tt.name, " --journal -- ", cast.ToJson(journal))
			assert.True(t, isValidJournal(journal), "invalid: %s", cast.ToJson(journal))
		})
	}
}

func Test_journalUseCase_addToJournalMock(t *testing.T) {
	mockJournalRepo := new(journalMock.Repository)
	mockProductRepo := new(productMock.Repository)

	accountDefaultJson := `[{"code":"4-40200","name":"Retur Penjualan","idx_key":"sles_retur","account_category_fkid":13},{"code":"1-10002","name":"Rekening Bank","idx_key":"sales_bank","account_category_fkid":3},{"code":"1-10002","name":"Rekening Bank","idx_key":"purchase_bank","account_category_fkid":3},{"code":"1-10100","name":"Piutang Usaha","idx_key":"purchase_debitmemo","account_category_fkid":1},{"code":"1-10100","name":"Piutang Usaha","idx_key":"piutang_usaha","account_category_fkid":1},{"code":"1-10101","name":"Piutang Belum Ditagih","idx_key":"sales_piutang","account_category_fkid":1},{"code":"1-10200","name":"Persediaan Barang Makanan","idx_key":"persediaan_persediaan","account_category_fkid":4},{"code":"8-80100","name":"Penyesuaian Persediaan","idx_key":"persediaan_umum","account_category_fkid":17},{"code":"5-50300","name":"Pengiriman & Pengangkutan","idx_key":"purchase_pengiriman","account_category_fkid":15},{"code":"6-60216","name":"Pengeluaran Barang Rusak","idx_key":"persediaan_rusak","account_category_fkid":16},{"code":"4-40000","name":"Pendapatan Makanan","idx_key":"sales_pendapatan","account_category_fkid":13},{"code":"7-70099","name":"Pendapatan Lain - lain","idx_key":"sales_pengiriman","account_category_fkid":14},{"code":"7-70099","name":"Pendapatan Lain - lain","idx_key":"pendapatan_lain","account_category_fkid":14},{"code":"2-20203","name":"Pendapatan Diterima Di Muka","idx_key":"sales_pembayaran","account_category_fkid":10},{"code":"4-40201","name":"Pendapatan Belum Ditagih","idx_key":"sales_penjualan","account_category_fkid":13},{"code":"1-10500","name":"PPN Masukan","idx_key":"purchase_tax","account_category_fkid":2},{"code":"2-20500","name":"PPN Keluaran","idx_key":"sales_tax","account_category_fkid":10},{"code":"3-30000","name":"Modal Saham","idx_key":"modal_saham","account_category_fkid":12},{"code":"1-10001","name":"Kas","idx_key":"sales_cash","account_category_fkid":3},{"code":"1-10001","name":"Kas","idx_key":"purchase_cash","account_category_fkid":3},{"code":"2-20100","name":"Hutang Usaha","idx_key":"hutang_usaha","account_category_fkid":8},{"code":"2-20101","name":"Hutang Belum Ditagih","idx_key":"purchase_hutang","account_category_fkid":8},{"code":"3-30999","name":"Ekuitas Saldo Awal","idx_key":"ekuitas_saldo_awal","account_category_fkid":12},{"code":"4-40100","name":"Diskon Penjualan","idx_key":"sales_diskon","account_category_fkid":13},{"code":"5-50500","name":"Biaya Produksi","idx_key":"persediaan_produksi","account_category_fkid":15},{"code":"1-10402","name":"Biaya Dibayar Di Muka","idx_key":"purchase_payment","account_category_fkid":2},{"code":"5-50000","name":"Beban Pokok Pendapatan Makanan","idx_key":"purchase_cogs","account_category_fkid":15},{"code":"8-80999","name":"Beban Lain - lain","idx_key":"purchase_operasional","account_category_fkid":17},{"code":"1-10705","name":"Aset Tetap - Perlengkapan Kantor","idx_key":"aset_tetap","account_category_fkid":5}]`
	salesWithTaxJson := `{"AdminId":7,"OutletId":440,"TransactionType":1,"TransactionId":"1671N73I3H808","TransactionTime":*************,"DiscountTotal":0,"TaxTotal":500,"Item":[{"PriceSell":25000,"PriceBuy":0,"Subtotal":25000,"ProductId":6558,"ProductDetailId":15387,"Discount":0}],"Payment":[{"Total":25500,"Method":"cash","BankName":""}],"TransactionStatus":"success","DiscountTransaction":0,"PiutangCredit":0}`
	salesStockMonitored := `{"AdminId":7,"OutletId":440,"TransactionType":1,"TransactionId":"1672H0H4HMA73","TransactionTime":*************,"DiscountTotal":0,"TaxTotal":0,"Item":[{"PriceSell":1500,"PriceBuy":0,"Subtotal":1500,"ProductId":6551,"ProductDetailId":15374,"Discount":0,"Product":{"product_id":6551,"name":"Biji Koppi Hitam","stock_management":1}}],"Payment":[{"Total":1500,"Method":"cash","BankName":""}],"TransactionStatus":"success","DiscountTransaction":0,"PiutangCredit":0}`
	salesStockUnmonitored := `{"AdminId":7,"OutletId":440,"TransactionType":1,"TransactionId":"1N72HM4515M64","TransactionTime":*************,"DiscountTotal":0,"TaxTotal":0,"Item":[{"PriceSell":25000,"PriceBuy":0,"Subtotal":25000,"ProductId":6558,"ProductDetailId":15387,"Discount":0,"Product":{"product_id":6558,"name":"Ikan Goreng"}}],"Payment":[{"Total":25000,"Method":"cash","BankName":""}],"TransactionStatus":"success","DiscountTransaction":0,"PiutangCredit":0}`
	salesMixMonitorAndUnonitor := `{"AdminId":7,"OutletId":440,"TransactionType":1,"TransactionId":"167178LNM72S3","TransactionTime":*************,"DiscountTotal":0,"TaxTotal":0,"Item":[{"PriceSell":1500,"PriceBuy":0,"Subtotal":1500,"ProductId":6551,"ProductDetailId":15374,"Discount":0,"Product":{"product_id":6551,"name":"Biji Koppi Hitam"}},{"PriceSell":0,"PriceBuy":30000,"Subtotal":0,"ProductId":6606,"ProductDetailId":15583,"Discount":0,"Product":{}},{"PriceSell":20000,"PriceBuy":0,"Subtotal":20000,"ProductId":6219,"ProductDetailId":14720,"Discount":0,"Product":{"product_id":6219,"name":"Ayam Goreng"}}],"Payment":[{"Total":21500,"Method":"cash","BankName":""}],"TransactionStatus":"success","DiscountTransaction":0,"PiutangCredit":0}`
	salesRefundStockMonitor := `{"AdminId":7,"OutletId":439,"TransactionType":1,"TransactionId":"16I30033N0K7H","TransactionTime":*************,"DiscountTotal":0,"TaxTotal":0,"Item":[{"PriceSell":5000,"PriceBuy":0,"Subtotal":5000,"ProductId":9352,"ProductDetailId":36708,"Discount":0,"Product":{"product_id":9352,"name":"Cappucino cincau","stock_management":1}}],"Payment":[{"Total":5000,"Method":"cash","BankName":""}],"TransactionStatus":"refund","DiscountTransaction":0,"PiutangCredit":0}`
	salesRefund2 := `{"AdminId":7,"OutletId":439,"TransactionType":1,"TransactionId":"1673M04O5K125","TransactionTime":*************,"DiscountTotal":0,"TaxTotal":0,"Item":[{"PriceSell":9500,"PriceBuy":0,"Subtotal":9500,"ProductId":9354,"ProductDetailId":36710,"Discount":0,"Product":{"product_id":9354,"name":"Kopasus Regel"}}],"Payment":[{"Total":9500,"Method":"cash","BankName":""}],"TransactionStatus":"refund","DiscountTransaction":0,"PiutangCredit":0}`
	salesRefundWithTax := `{"AdminId":143,"OutletId":761,"TransactionType":1,"TransactionId":"1N7IH86559A4H","TransactionTime":*************,"DiscountTotal":2000,"TaxTotal":2000,"Item":[{"PriceSell":10000,"PriceBuy":0,"Subtotal":20000,"ProductId":55085,"ProductDetailId":125327,"Discount":0,"Product":{"product_id":55085,"name":"Ayam Potong Goreng"}}],"Payment":[{"Total":20000,"Method":"card","BankName":"BCA","BankId":0}],"TransactionStatus":"refund","DiscountTransaction":0,"PiutangCredit":0,"TaxDetail":[{"sales_tax_id":0,"tax_fkid":0,"total":2000,"sales_fkid":"","category":"discount","gratuity_id":171,"name":"diskon hari minggu","tax_category":"","tax_status":"","tax_type":"","jumlah":0,"admin_fkid":0,"data_created":0,"data_modified":0,"data_status":""},{"sales_tax_id":0,"tax_fkid":0,"total":2000,"sales_fkid":"","category":"service","gratuity_id":109,"name":"service10%","tax_category":"","tax_status":"","tax_type":"","jumlah":0,"admin_fkid":0,"data_created":0,"data_modified":0,"data_status":""}],"Description":"LMH202302272"}`
	salesPaymentCard := `{"AdminId":1,"OutletId":364,"TransactionType":1,"TransactionId":"INV-ONS2023012414","TransactionTime":*************,"DiscountTotal":0,"TaxTotal":0,"Item":[{"PriceSell":12000,"PriceBuy":0,"Subtotal":12000,"ProductId":4975,"ProductDetailId":11249,"Discount":0,"Product":{"product_id":4975,"name":"Ayam Geprek Paha"}},{"PriceSell":19000,"PriceBuy":0,"Subtotal":19000,"ProductId":5732,"ProductDetailId":12971,"Discount":0,"Product":{"product_id":5732,"name":"Kopi Masa Lalu"}},{"PriceSell":19000,"PriceBuy":0,"Subtotal":19000,"ProductId":5731,"ProductDetailId":12970,"Discount":0,"Product":{"product_id":5731,"name":"Kopi Mantan"}}],"Payment":[{"Total":50000,"Method":"card","BankName":"QRIS"}],"TransactionStatus":"success","DiscountTransaction":0,"PiutangCredit":0}`
	salesRefund := `{"AdminId":1,"OutletId":29,"TransactionType":1,"TransactionId":"167S0S3H9SL44","TransactionTime":*************,"DiscountTotal":0,"TaxTotal":3242,"Item":[{"PriceSell":32423,"PriceBuy":1000,"Subtotal":32458,"ProductId":6230,"ProductDetailId":14747,"Discount":0,"Product":{"product_id":6230,"name":"ABC GAMBAR 2","stock_management":1}}],"Payment":[{"Total":35700,"Method":"cash","BankName":""}],"TransactionStatus":"refund","DiscountTransaction":0,"PiutangCredit":0}`
	discItemAndSales := `{"AdminId":143,"OutletId":761,"TransactionType":1,"TransactionId":"167I4L02HL154","TransactionTime":*************,"DiscountTotal":3080,"TaxTotal":1880,"Item":[{"PriceSell":0,"PriceBuy":5000,"Subtotal":0,"ProductId":55118,"ProductDetailId":125516,"Discount":0,"Product":{"product_id":55118,"name":"Bahan 1","stock_management":1}},{"PriceSell":0,"PriceBuy":6000,"Subtotal":0,"ProductId":55119,"ProductDetailId":125517,"Discount":0,"Product":{"product_id":55119,"name":"Bahan 2","stock_management":1}},{"PriceSell":0,"PriceBuy":4900,"Subtotal":0,"ProductId":55120,"ProductDetailId":129277,"Discount":0,"Product":{"product_id":55120,"name":"Bahan 3","stock_management":1}},{"PriceSell":10000,"PriceBuy":0,"Subtotal":18800,"ProductId":55085,"ProductDetailId":125327,"Discount":1200,"Product":{"product_id":55085,"name":"Ayam Potong Goreng"}}],"Payment":[{"Total":18800,"Method":"card","BankName":"BRI","BankId":0}],"TransactionStatus":"success","DiscountTransaction":0,"PiutangCredit":0,"TaxDetail":[{"sales_tax_id":0,"tax_fkid":0,"total":1880,"sales_fkid":"","category":"service","gratuity_id":109,"name":"service10%","tax_category":"","tax_status":"","tax_type":"","jumlah":0,"admin_fkid":0,"data_created":0,"data_modified":0,"data_status":""},{"sales_tax_id":0,"tax_fkid":0,"total":1880,"sales_fkid":"","category":"discount","gratuity_id":171,"name":"diskon hari minggu","tax_category":"","tax_status":"","tax_type":"","jumlah":0,"admin_fkid":0,"data_created":0,"data_modified":0,"data_status":""}],"Description":"LMH202302274"}`

	type args struct {
		transaction string
	}

	tests := []struct {
		name string
		args args
		want int
	}{
		{"salesWithTax", args{salesWithTaxJson}, 1},
		{"salesStockMonitored", args{salesStockMonitored}, 1},
		{"salesStockUnmonitored", args{salesStockUnmonitored}, 1},
		{"salesMixMonitorAndUnonitor", args{salesMixMonitorAndUnonitor}, 1},
		{"salesRefundStockMonitor", args{salesRefundStockMonitor}, 1},
		{"salesRefund2", args{salesRefund2}, 1},
		{"salesRefundWithTax", args{salesRefundWithTax}, 1},
		{"salesPaymentCard", args{salesPaymentCard}, 1},
		{"salesRefund", args{salesRefund}, 1},
		{"discItemAndSales", args{discItemAndSales}, 1},
	}

	var result []domain.AccountDefault
	err := json.Unmarshal([]byte(accountDefaultJson), &result)
	assert.NoError(t, err)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			var journalTransaction domain.JournalTransaction
			err = json.Unmarshal([]byte(tt.args.transaction), &journalTransaction)
			assert.NoError(t, err)

			// productDetailIds := make([]int, 0)
			// for _, item := range journalTransaction.Item {
			// 	productDetailIds = append(productDetailIds, item.ProductDetailId)
			// }

			mockJournalRepo.On("FetchAccountDefault", journalTransaction.AdminId).Return(result, nil)
			mockJournalRepo.On("GetAccountByProduct", mock.Anything).Return([]domain.AccountSetting{})
			mockJournalRepo.On("InsertJournals", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()
			j := journalUseCase{mockJournalRepo, mockProductRepo}
			journal := j.formatJournal(journalTransaction)
			fmt.Println(">>> ", tt.name, " --journal -- ", cast.ToJson(journal))
			assert.True(t, isValidJournal(journal), "invalid: %s", cast.ToJson(journal))
		})
	}
}

func Test_journalUseCase_addToJournal(t *testing.T) {
	type fields struct {
		repo journal.Repository
	}
	type args struct {
		transaction domain.JournalTransaction
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			j := journalUseCase{
				repo: tt.fields.repo,
			}
			j.addTransactionToJournal(tt.args.transaction)
		})
	}
}
