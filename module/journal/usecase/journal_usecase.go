package usecase

import (
	"fmt"
	"math"
	"os"
	"sync"
	"time"

	"github.com/go-co-op/gocron"
	"gitlab.com/uniqdev/backend/service-finance/core/log"
	"gitlab.com/uniqdev/backend/service-finance/core/util/cast"
	domain "gitlab.com/uniqdev/backend/service-finance/domain"
	"gitlab.com/uniqdev/backend/service-finance/module/journal"
	"gitlab.com/uniqdev/backend/service-finance/module/product"
)

var muJob sync.Mutex

type journalUseCase struct {
	repo        journal.Repository
	repoProduct product.Repository
}

func NewJournalUseCase(repository journal.Repository, repoProduct product.Repository, cron *gocron.Scheduler) journal.JournalUseCase {
	uc := &journalUseCase{repository, repoProduct}
	uc.initCron(cron)
	return uc
}

func (j *journalUseCase) RunInvalidJournal() {
	muJob.Lock()
	defer muJob.Unlock()

	failures, err := j.repo.FetchJournalFailure()
	if log.IfError(err) {
		return
	}

	fmt.Println("total journal failures ", len(*failures))
	for _, fail := range *failures {
		if fail.TransType == domain.TransTypeSales {
			j.ReceiveNewSales(fail.TransID)
		} else if fail.TransType == domain.TransTypeProduction {
			j.ReceiveNewProduction(cast.ToInt(fail.TransID))
		}
	}
}

func (j *journalUseCase) RunJob() {
	// coa.LoadCoa(j.repo.GetDefaultAccount())
}

func (j *journalUseCase) GetPurchase(purchaseId string) []domain.DataPurchase {
	return []domain.DataPurchase{}
}

// active means: enabled on the environment, and user has subscription
func (j *journalUseCase) isFinanceActive(adminId int) bool {
	//check if current environement is suitable to run finance
	// envAllowed := array.In([]string{"development", "localhost", "staging"}, os.Getenv("ENV"))
	// if !envAllowed && os.Getenv("ENABLE_FINANCE") != "true" {
	// 	log.Info("finance not running on env: %v | enable: %v", os.Getenv("ENV"), os.Getenv("ENABLE_FINANCE"))
	// 	return false
	// }

	//TODO: check if billing of Finance is active
	subs, err := j.repo.FetchUserSubscription(adminId, "finance")
	log.IfError(err)

	log.Info("finance sub for '%v' : %v", adminId, len(subs))
	return len(subs) > 0
}

func (j *journalUseCase) addTransactionToJournal(transaction domain.JournalTransaction) {
	journals := j.formatJournal(transaction)

	//in case the transaction logged in journal failure, delete it first
	log.IfError(j.repo.DeleteJournalFailure(cast.ToString(transaction.TransactionId)))

	//check the validity, if ok, then do the insert
	if isValidJournal(journals) {
		if os.Getenv("env") == "localhost" {
			fmt.Println("valid journal! --> ", cast.ToJson(journals))
			fmt.Println("saving skipped... for env", os.Getenv("env"))
			return
		}

		// transTypeId, err := j.repo.FetchTransactionType(transaction.TransactionType)
		// if err != nil || transTypeId == 0 {
		// 	log.IfError(fmt.Errorf("FetchTransactionType error, result: %v | err: %v", transTypeId, err))
		// 	return
		// }

		//fill the rest of data, as all has the same values
		for i, journal := range journals {
			if journal.Nominal == 0 {
				continue
			}

			journals[i].TransId = cast.ToString(transaction.TransactionId)
			journals[i].TransType = transaction.TransactionType
			journals[i].AdminFkid = transaction.AdminId
			journals[i].OutletFkid = transaction.OutletId
			journals[i].TransCreated = transaction.TransactionTime
			journals[i].Description = transaction.Description
		}

		log.Info("%s valid journal: %v", transaction.TransactionId, cast.ToJson(journals))
		j.repo.InsertJournals(journals...)
	} else {
		//if journal invalid, save the id to database in order to be able to retry later on
		fmt.Printf("invalid journal -->\n%s\n", cast.ToJson(journals))
		log.IfError(fmt.Errorf("invalid journal, transId: %v (%v)", transaction.TransactionId, transaction.TransactionType))

		if os.Getenv("env") == "localhost" {
			return
		}
		j.repo.InsertJournalFailures(domain.JournalFailures{
			TransType: transaction.TransactionType,
			TransID:   cast.ToString(transaction.TransactionId),
			Info:      fmt.Sprintf("%s\n%s", cast.ToJson(transaction), cast.ToJson(journals)),
			CreatedAt: time.Now().Unix() * 1000,
		})
	}
}

func (j *journalUseCase) formatJournal(transaction domain.JournalTransaction) []domain.Journal {
	fmt.Println("---> format transaction for journal: ", cast.ToJson(transaction))
	journals := make([]domain.Journal, 0)
	isReturn := transaction.TransactionStatus == "refund"
	logLine := log.Line()

	accountDefaults, err := j.repo.FetchAccountDefault(transaction.AdminId)
	log.IfError(err)

	accontDefaultMap := make(map[string]domain.AccountDefault)
	for _, account := range accountDefaults {
		accontDefaultMap[account.IdxKey] = account
	}

	// fmt.Println("---> account defaults: ", cast.ToJson(accontDefaultMap))

	totalPayment := 0
	for _, p := range transaction.Payment {
		var account domain.AccountDefault
		if p.Method == "cash" {
			account = accontDefaultMap[domain.KeySalesCash]
		} else if p.Method == "piutang" {
			account = accontDefaultMap[domain.KeyPiutangUsaha]
		} else if p.Method == "card" {
			//if default account set, use the dafault account instead
			if accountSetting, ok := accontDefaultMap[fmt.Sprintf("%v#%v", domain.AccountTypePayment, p.BankId)]; ok {
				account = accountSetting
				log.Info("account setting found for: %v (%v) --> %v", domain.AccountTypePayment, p.BankId, accountSetting)
			} else {
				account = accontDefaultMap[domain.KeySalesBank]
			}
		} else if p.Method == "compliment" {
			account = domain.AccountDefault{
				Code:                "4-40101",
				Name:                "Compliment",
				AccountCategoryFkid: 13,
			}
		} else if p.Method == "duty meals" {
			account = domain.AccountDefault{
				Code:                "4-40102",
				Name:                "Duty Meals",
				AccountCategoryFkid: 13,
			}
		}

		journals = append(journals, domain.Journal{
			AccountCode:         account.Code,
			AccountName:         account.Name,
			Type:                accountType(!isReturn),
			Nominal:             float64(p.Total),
			AccountCategoryFkid: account.AccountCategoryFkid,
		})
		logLine.AddLog(fmt.Sprintf("%v \n", journals[len(journals)-1]))
		totalPayment += p.Total
	}

	account := accontDefaultMap[domain.KeyPiutangUsaha]
	journals = append(journals, domain.Journal{
		AccountCode:         account.Code,
		AccountName:         account.Name,
		Type:                "K",
		Nominal:             float64(transaction.PiutangCredit),
		AccountCategoryFkid: account.AccountCategoryFkid,
	})
	logLine.AddLog(fmt.Sprintf("%v \n", journals[len(journals)-1]))
	// log.Info("id: %v - %d", account.AccountCategoryFkid, journals[len(journals)-1].Nominal)

	account = accontDefaultMap[domain.KeySalesDiskon]
	journals = append(journals, domain.Journal{
		AccountCode:         account.Code,
		AccountName:         account.Name,
		Type:                accountType(!isReturn),
		Nominal:             float64(transaction.DiscountTotal),
		AccountCategoryFkid: account.AccountCategoryFkid,
	})
	logLine.AddLog(fmt.Sprintf("%v \n", journals[len(journals)-1]))

	totalSubTotalUnmonitored := 0
	totalDiscPerItemUnmonitored := 0

	priceBuyTotal := float64(0)
	totalSubTotal := 0
	totalDiscPerItem := 0
	productIds := make([]int, 0)

	for _, item := range transaction.Item {
		productIds = append(productIds, item.ProductId)
	}

	//get custom account codes
	accountSettings := j.repo.GetAccountByProduct(productIds)

	productExpanseMap := make(map[int]int)
	accountSettingMap := make(map[int]domain.AccountSetting)
	for _, account := range accountSettings {
		productExpanseMap[account.ProductFkid] = account.Expenses
		accountSettingMap[account.ProductFkid] = account
	}

	for _, item := range transaction.Item {
		// fmt.Println(item.Product.Name, "--", item.Product.StockManagement)
		if item.Product.StockManagement == 1 && productExpanseMap[item.ProductId] == 0 {
			priceBuyTotal += item.PriceBuy
			totalSubTotal += item.Subtotal
			totalDiscPerItem += item.Discount
		} else {
			totalSubTotalUnmonitored += item.Subtotal
			totalDiscPerItemUnmonitored += item.Discount
		}
	}

	fmt.Println("totalUnmonitoredStock --> ", totalSubTotalUnmonitored)

	priceBuyTotalSetting := float64(0)
	penjualanTotalSetting := 0
	fmt.Println(">> custom account setting: ", cast.ToJson(accountSettings))
	for _, item := range transaction.Item {
		account, ok := accountSettingMap[item.ProductId]
		if !ok || item.PriceBuy == 0 {
			continue
		}

		priceBuy := float64(0)
		priceBuyTotalSetting += item.PriceBuy
		priceBuy = item.PriceBuy

		//pendapatan
		// journals = append(journals, domain.Journal{
		// 	AccountCode:         account.AccountKredit,
		// 	AccountName:         account.CreditName,
		// 	Type:                accountType(isReturn),
		// 	Nominal:             float64(priceBuy), //float64(totalSubTotal + totalDiscPerItem), //float64(priceBuy),
		// 	AccountCategoryFkid: account.CreditCategoryId,
		// })
		// logLine.AddLog(fmt.Sprintf("%v - %v (%v) \n", journals[len(journals)-1], item.ProductDetailId, item.Subtotal))
		// fmt.Printf(">>> nominal: %d | acountc name: %v \n", priceBuy, account.DebitName)

		isStockMonitored := item.Product.StockManagement == 1 && productExpanseMap[item.ProductId] == 0
		logLine.AddLog(fmt.Sprintf("%v isStockMonitored: %v, sm: %v, expanse: %v\n", item.ProductId, isStockMonitored, item.Product.StockManagement, productExpanseMap[item.ProductId]))
		//persediaan - mengurangi persediaan
		//hanya jika stock-management Yes
		if isStockMonitored {
			journals = append(journals, domain.Journal{
				AccountCode:         account.InventoryCode,
				AccountName:         account.InventoryName,
				Type:                accountType(isReturn),
				Nominal:             float64(priceBuy),
				AccountCategoryFkid: account.InventoryCategoryId,
			})
			logLine.AddLog(fmt.Sprintf("%v \n", journals[len(journals)-1]))
		}

		//beban
		// fmt.Println("----------------- transaction.TransactionType: ", transaction.TransactionType)
		if isStockMonitored && transaction.TransactionType == 1 {
			log.Info(">> penjualan = %d + %d\n", totalSubTotal, totalDiscPerItem)
			penjualanTotalSetting = totalSubTotal + totalDiscPerItem
			journals = append(journals, domain.Journal{
				AccountCode:         account.AccountDebit,
				AccountName:         account.DebitName,
				Type:                accountType(!isReturn),
				Nominal:             float64(priceBuy), //float64(totalSubTotal + totalDiscPerItem),
				AccountCategoryFkid: account.DebitCategoryId,
			})
			logLine.AddLog(fmt.Sprintf("%v \n", journals[len(journals)-1]))
		}
	}

	//beban pokok penjualan
	account = accontDefaultMap[domain.KeyPurhcaseCogs]
	journals = append(journals, domain.Journal{
		AccountCode:         account.Code,
		AccountName:         account.Name,
		Type:                accountType(!isReturn),
		Nominal:             float64(priceBuyTotal - priceBuyTotalSetting), //float64(priceBuyTotal),
		AccountCategoryFkid: account.AccountCategoryFkid,
	})
	logLine.AddLog(fmt.Sprintf("%v \n", journals[len(journals)-1]))
	// log.Info("id: %v - %v", account.AccountCategoryFkid, journals[len(journals)-1].Nominal)

	//persediaan
	log.Info("%v - %v = %v", priceBuyTotal, priceBuyTotalSetting, priceBuyTotal-priceBuyTotalSetting)
	account = accontDefaultMap[domain.KeyPersediaanPersediaan]
	journals = append(journals, domain.Journal{
		AccountCode:         account.Code,
		AccountName:         account.Name,
		Type:                accountType(isReturn),
		Nominal:             float64(priceBuyTotal - priceBuyTotalSetting),
		AccountCategoryFkid: account.AccountCategoryFkid,
	})
	logLine.AddLog(fmt.Sprintf("%v \n", journals[len(journals)-1]))
	// log.Info("id: %v - %v", account.AccountCategoryFkid, journals[len(journals)-1].Nominal)
	//
	//Pendapatan
	// account = accontDefaultMap[domain.KeySalesPendapatan]
	// journals = append(journals, domain.Journal{
	// 	AccountCode:         "4-40000",
	// 	AccountName:         "Pendapatan",
	// 	Type:                accountType(isReturn),                                                                              //!isReturn
	// 	Nominal:             float64(totalSubTotal + totalDiscPerItem + totalSubTotalUnmonitored + totalDiscPerItemUnmonitored), //float64(totalSubTotalUnmonitored + totalDiscPerItemUnmonitored),
	// 	AccountCategoryFkid: 13,
	// })
	// logLine.AddLog(fmt.Sprintf("%v \n", journals[len(journals)-1]))
	// log.Info("id: %v - %v", account.AccountCategoryFkid, journals[len(journals)-1].Nominal)
	fmt.Println(penjualanTotalSetting)

	//penjualan or pendapatan
	account = accontDefaultMap[domain.KeySalesPendapatan]
	if transaction.TransactionType == 1 {
		fmt.Printf("penjualan = %d + %d -> %v (%v) \n", totalSubTotal, totalDiscPerItem, account.Name, account.Code)
		journals = append(journals, domain.Journal{
			AccountCode:         account.Code,
			AccountName:         account.Name,
			Type:                accountType(isReturn),
			Nominal:             float64(totalSubTotal + totalDiscPerItem + totalSubTotalUnmonitored + totalDiscPerItemUnmonitored), //float64((totalSubTotal + totalDiscPerItem) - penjualanTotalSetting),
			AccountCategoryFkid: account.AccountCategoryFkid,
		})
		logLine.AddLog(fmt.Sprintf("%v \n", journals[len(journals)-1]))
	}

	//tax
	totalTaxNoAccountSetting := transaction.TaxTotal
	for _, tax := range transaction.TaxDetail {
		if account, ok := accontDefaultMap[fmt.Sprintf("%v#%v", domain.AccountTypeGratuity, tax.GratuityID)]; ok {
			log.Info("account setting gratuity found: %v#%v", domain.AccountTypeGratuity, tax.GratuityID)
			totalTaxNoAccountSetting -= tax.Total
			journals = append(journals, domain.Journal{
				AccountCode:         account.Code,
				AccountName:         account.Name,
				Type:                accountType(isReturn),
				Nominal:             float64(tax.Total),
				AccountCategoryFkid: account.AccountCategoryFkid,
			})
			logLine.AddLog(fmt.Sprintf("%v \n", journals[len(journals)-1]))
		}
	}

	account = accontDefaultMap[domain.KeySalesTax]
	journals = append(journals, domain.Journal{
		AccountCode:         account.Code,
		AccountName:         account.Name,
		Type:                accountType(isReturn),
		Nominal:             float64(totalTaxNoAccountSetting),
		AccountCategoryFkid: account.AccountCategoryFkid,
	})
	logLine.AddLog(fmt.Sprintf("%v \n", journals[len(journals)-1]))

	result := make([]domain.Journal, 0)
	for _, journal := range journals {
		if journal.Nominal > 0 {
			result = append(result, journal)
		}
	}

	if os.Getenv("ENV") == "localhost" {
		logLine.Print()
	}

	return result
}

// make sure debit and credit has the same amount
func isValidJournal(journal []domain.Journal) bool {
	total := map[string]float64{}
	for _, j := range journal {
		if j.Nominal > 0 && (j.AccountCode == "" || j.AccountName == "") {
			return false
		}

		if j.AccountCode == "" || j.AccountName == "" {
			continue
		}
		total[j.Type] += j.Nominal
	}
	diff := math.Abs(total["D"] - total["K"])
	fmt.Printf("diff: %v --> %v\n", diff, total)
	// return total["D"] == total["K"]
	return diff == 0 || (diff < 0.01 && diff >= 0) || diff < 500
}

func accountType(isAdd bool) string {
	if isAdd {
		return "D"
	}
	return "K"
}

//	func filterAccount(accounts []domain.AccountDefault, accountType string, id int) *domain.AccountDefault {
//		for _, account := range accounts {
//			if account.Type == accountType && id == account.TypeFkid {
//				return &account
//			}
//		}
//		return nil
//	}
func removeZeroJournal(journals []domain.Journal) []domain.Journal {
	var filteredJournals []domain.Journal

	for _, journal := range journals {
		if journal.Nominal != 0 {
			filteredJournals = append(filteredJournals, journal)
		}
	}

	return filteredJournals
}
