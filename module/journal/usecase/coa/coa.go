package coa

import (
	"regexp"

	"gitlab.com/uniqdev/backend/service-finance/core/util/cast"

	"gitlab.com/uniqdev/backend/service-finance/domain"
)

type Journal int
type BankDetail struct {
	Name string
}

const (
	AktivaLancar Journal = iota
	KasdanBank
	KasKecil
	Bank
	Piutang
	PiutangUsaha
	PiutangKaryawan
	PiutangLainlain
	CadanganKerugianPiutang
	Persediaan
	PersediaanBahanBaku
	PersediaanBahanSetengahJadi
	PersediaanBahanJadi
	PersediaanBahanPelengkap
	GoodReceiptInvoiceReceipt
	Perlengkapan
	PerlengkapanKantor
	PerlengkapanOutlet
	BebanDibayarDiMuka
	SewaDibayarDiMuka
	IklanDibayarDiMuka
	AktivaLancarlainnya
	UangMukaPembelian
	PPNMasukan
	PajakDibayarDiMukaPPh22
	PajakDibayarDiMukaPPh23
	PajakDibayarDiMukaPPh25
	AktivaTidakLancar
	Tanah
	Bangunan
	Kendaraan
	KendaraanOperasionalKantor
	KendaraanOperasionalOutlet
	MesindanPeralatan
	MesindanPeralatanKantor
	MesindanPeralatanOutlet
	KonstruksiDalamPengerjaan
	AsetTakBerwujud
	AkumulasiPenyusutanBangunan
	AkumulasiPenyusutanKendaraan
	AkumulasiPenyusutanKendaraanOperasionalKantor
	AkumulasiPenyusutanKendaraanOperasionalOutlet
	AkumulasiPenyusutanMesindanPeralatan
	AkumulasiPenyusutanMesindanPeralatanKantor
	AkumulasiPenyusutanMesindanPeralatanOutlet
	AkumulasiAmortisasi
	AsetTidakLancarLainnya
	AsetPajakTangguhan
	PasifaLancar
	UtangUsaha
	UtangBankJangkaPendek
	UtangPajak
	PPNKeluaran
	PajakDaerahPB1
	UtangPajakPPh21
	UtangPajakPPh22
	UtangPajakPPh23
	UtangPajakPPh29
	UtangPajakLainnya
	UtangJangkaPendekkePemilikPemegangSaham
	PendapatanDiterimaDimuka
	UtangJangkaPendekLainnya
	UtangGajiBonus
	UtangBPJS
	UtangUtilitasListrikAirdanInternet
	UtangDividen
	UtangPihakke3
	PasifaTidakLancar
	UtangBankJangkaPanjang
	UtangJangkaPanjangkePemilikPemegangSaham
	UtangJangkaPanjangLainnya
	UtangPajakTangguhan
	Beban
	BebanPokokPenjualan
	DiskonPembelian
	ReturPembelian
	BebanPenjualan
	BebanGaji
	BebanTHRdanBonus
	BebanBPJS
	BebanSewa
	BebanUtilitasListrikAir
	BebanPenyusutanBangunan
	BebanPenyusutanKendaraan
	BebanPenyusutanMesindanPeralatan
	BebanIklan
	BebanPenjualanLainnya
	BebanJasaMaklon
	BebanUmumdanAdministrasi
	BebanAsuransi
	BebanATK
	BebanPerijinan
	BebanKomunikasiTelepon
	BebanJasaProfesional
	BebanPelatihandanPengembanganKaryawan
	BebanPerjalananDinas
	BebanUmumdanAdministrasiLainnya
	PENDAPATAN
	Penjulan
	PendapatanMinuman
	PendapatanJasaMaklon
	PendapatanFranchise
	PendapatanKomisiBarangKonsinyasi
	DiskonPenjualan
	ReturPenjualan
	Ekuitas
	ModalSaham
	ModalSahamNamaPerusahaanPeroranganPemegangSaham
	ModalSahamX
	TambahanModalDisetor
	TambahanModalDisetorNamaPerusahaanPeroranganPemegangSaham
	TambahanModalDisetorX
	LabaDitahan
	LabaRugiTahunBerjalan
	Dividen
	PenghasilanKomprehensifLainnya
	KomponenEkuitasLainnya
	RevaluasiAset
	PerubahanKebijakanAkuntansi
	Koreksi
	PendapatanLainLain
	PendapatanBunga
	PendapatanInvestasi
	PendapatanDividen
	LabaRugiAtasPenjualanAsetTetap
	Pembulatan
	BebanLainLain
	BebanBungadanProvisi
	BebanBunga
	BebanProvisi
	PenyesuaianPersediaan
	BebanPajak
)

var coaList = make([]domain.CoaJournal, 0)

func (j Journal) AccountName() string {
	if len(coaList) == 0 {
		return ""
	}
	return coaList[j].NamaAkun
}

func (j Journal) AccountCode() string {
	if len(coaList) == 0 {
		return ""
	}

	return coaList[j].KodeAkun
}

func (j Journal) AccountCategoryId() int {
	return cast.ToInt(coaList[j].AccountCategoryFkid)
}

func (j Journal) Type(isAdd bool) string {
	if isAdd {
		return coaList[j].Bertambah
	} else {
		return coaList[j].Berkurang
	}
}

func Get(name string) Journal {
	for i, coa := range coaList {
		if coa.NamaAkun == name {
			return Journal(i)
		}
	}
	return 0
}

func LoadCoa(data []domain.CoaJournal) {
	keys := make(map[string]bool)
	coaList = make([]domain.CoaJournal, 0)
	re := regexp.MustCompile("[^[A-Za-z0-9]")
	for _, coa := range data {
		name := re.ReplaceAllString(coa.NamaAkun, "")
		if !keys[name] {
			// fmt.Println(name)
			keys[name] = true
			coaList = append(coaList, coa)
		}
	}
}
