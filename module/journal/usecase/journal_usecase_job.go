package usecase

import (
	"fmt"
	"sync"
	"time"

	"github.com/go-co-op/gocron"
	"gitlab.com/uniqdev/backend/service-finance/core/log"
	"gitlab.com/uniqdev/backend/service-finance/core/util/cast"
	"gitlab.com/uniqdev/backend/service-finance/domain"
)

var muMissJob sync.Mutex

func (j *journalUseCase) initCron(cron *gocron.Scheduler) {
	cron.Every(1).Day().At("19:15").Do(j.RunInvalidJournal)    //run everyday at 19:15
	cron.Every(1).Day().At("19:45").Do(j.CheckMissingJournals) //run everyday at 19:45
}

func (j *journalUseCase) CheckMissingJournals(params ...domain.MissingJournalParam) {
	muMissJob.Lock()
	defer muMissJob.Unlock()

	var param domain.MissingJournalParam
	if len(params) > 0 {
		param = params[0]
	} else {
		// Get yesterday's timestamp range (UTC+7)
		now := time.Now().In(time.FixedZone("WIB", 7*60*60))
		intervalDay := int64(24 * 60 * 60 * 1000)                                                        //one day in milliseconds
		endTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).UnixMilli() //get current time, but at 00:00:00
		startTime := endTime - intervalDay
		param = domain.MissingJournalParam{
			StartTime: startTime,
			EndTime:   endTime,
			AdminId:   10,
		}
	}

	log.Info("CheckMissingJournals: %v", cast.ToJson(param))

	// Fetch missing journals
	missingJournals, err := j.repo.GetMissingJournals(param.StartTime, param.EndTime, param.AdminId)
	if err != nil {
		log.IfError(err)
		return
	}

	log.Info("missing journals: %v", len(missingJournals))

	// Insert failures for each missing journal
	for _, salesId := range missingJournals {
		j.repo.InsertJournalFailures(domain.JournalFailures{
			TransType: domain.TransTypeSales, // Using constant 1 for sales as defined in the query
			TransID:   salesId,
			Info:      fmt.Sprintf("Missing journal entry for sales ID: %s", salesId),
			CreatedAt: time.Now().UnixMilli(),
		})
	}
}

// ConvertTransType converts trans_type from 1 to 32 for journals with trans_id containing '.'
func (j *journalUseCase) ConvertTransType() error {
	const (
		batchSize    = 20
		newTransType = 32
	)

	log.Info("Starting trans_type conversion process")

	totalProcessed := 0
	for {
		// Fetch a batch of journal IDs that need conversion
		ids, err := j.repo.FetchJournalsForTransTypeConversion(batchSize)
		if err != nil {
			log.IfError(fmt.Errorf("failed to fetch journals for conversion: %v", err))
			return err
		}

		// If no more records to process, break the loop
		if len(ids) == 0 {
			log.Info("No more journals to convert. Total processed: %d", totalProcessed)
			break
		}

		// Update the trans_type for this batch
		err = j.repo.UpdateTransType(ids, newTransType)
		if err != nil {
			log.IfError(fmt.Errorf("failed to update trans_type for batch: %v", err))
			return err
		}

		totalProcessed += len(ids)
		log.Info("Converted %d journals in this batch. Total processed so far: %d", len(ids), totalProcessed)

		// Small delay to prevent overwhelming the database
		time.Sleep(100 * time.Millisecond)
	}

	log.Info("Trans_type conversion completed successfully. Total records processed: %d", totalProcessed)
	return nil
}
