package usecase

import (
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"time"

	"gitlab.com/uniqdev/backend/service-finance/core/exceptions"
	"gitlab.com/uniqdev/backend/service-finance/core/log"
	"gitlab.com/uniqdev/backend/service-finance/core/util/cast"
	"gitlab.com/uniqdev/backend/service-finance/core/util/number"
	"gitlab.com/uniqdev/backend/service-finance/domain"
	"gitlab.com/uniqdev/backend/service-finance/model"
)

type productionData struct {
	AccountDefaultMap      map[string]domain.AccountDefault
	Production             model.ProductionEntity
	ProductionDetail       []model.ProductionDetailEntity
	ProductionCost         []model.ProductionCostEntity
	PurcahseReportCategory []model.PurchaseReportCategoryEntity
}

func (j journalUseCase) ReceiveNewProduction(productionId int) {
	data, err := j.getProductionData(productionId)
	if err != nil {
		fmt.Println("journal production not created: ", err)
		return
	}

	journals, err := j.generateJournalProductionDebt(data)
	if err != nil {
		fmt.Println("generateJournalProductionDebt err: ", err)
	}
	log.Info("journal debt: %v", len(journals))

	journalsItem, err := j.generateJournalProductionItem(data)
	if err != nil {
		fmt.Println("generateJournalProductionItem err: ", err)
	}

	log.Info("journal item: %d", len(journalsItem))
	journals = append(journals, journalsItem...)

	log.IfError(j.repo.DeleteJournalFailure(cast.ToString(productionId)))
	if isValidJournal(journals) {
		j.repo.InsertJournals(journals...)
	} else {
		jsonJournal, _ := json.Marshal(journals)
		log.Info(">>> invalid journal: %s", string(jsonJournal))
		j.repo.InsertJournalFailures(domain.JournalFailures{
			TransType: 3,
			TransID:   cast.ToString(productionId),
			Info:      cast.ToJson(journals),
			CreatedAt: time.Now().Unix() * 1000,
		})
	}
}

func (j journalUseCase) generateJournalProductionItem(data productionData) ([]domain.Journal, error) {
	journals := make([]domain.Journal, 0)

	purchaseReportMap := make(map[int64]model.PurchaseReportCategoryEntity)
	for _, category := range data.PurcahseReportCategory {
		purchaseReportMap[int64(category.PurchaseReportCategoryID)] = category
	}

	totalCostLeft := 0
	for _, cost := range data.ProductionCost {
		if account, ok := data.AccountDefaultMap[fmt.Sprintf("prc_category#%v", cost.PurchaseReportCategoryFKID)]; ok {
			journals = append(journals, domain.Journal{
				AccountCode:         account.Code,
				AccountName:         account.Name,
				Type:                "K",
				Nominal:             float64(cost.Nominal),
				AccountCategoryFkid: account.AccountCategoryFkid,
				Description:         purchaseReportMap[cost.PurchaseReportCategoryFKID].Name,
			})
		} else {
			totalCostLeft += cost.Nominal
		}
	}

	journals = append(journals, domain.Journal{
		AccountCode:         "8-80999",
		AccountName:         "Beban Lain - lain",
		Type:                "K",
		Nominal:             float64(totalCostLeft),
		AccountCategoryFkid: 17,
	})

	productDetailIds := make([]int, 0)
	for _, detail := range data.ProductionDetail {
		productDetailIds = append(productDetailIds, int(detail.ProductDetailFKID))
	}
	productDetailIds = append(productDetailIds, int(data.Production.ProductDetailFKID))

	productDetail, err := j.repoProduct.FetchProductDetail(productDetailIds...)
	if log.IfError(err) {
		return journals, err
	}

	productIds := make([]int, 0)
	for _, product := range productDetail {
		productIds = append(productIds, product.ProductFkid)
	}

	products, err := j.repo.FetchProduct(productIds...)
	log.IfError(err)

	// Create a map to store the products
	productMap := make(map[int]domain.Product)
	for _, product := range products {
		productMap[product.ProductId] = product
	}

	productDetailMap := make(map[int]model.ProductDetailEntity)
	for _, pd := range productDetail {
		productDetailMap[pd.ProductDetailID] = pd
	}

	accountSettingProduct := j.repo.GetAccountByProduct(productIds)
	fmt.Println("total account setting product: ", len(accountSettingProduct))

	// Group accountSettingProduct by ProductDetailFkid
	accountSettingMap := make(map[int]domain.AccountSetting)
	for _, account := range accountSettingProduct {
		accountSettingMap[account.ProductFkid] = account
	}

	totalIngredientLeft := float64(0)
	for _, item := range data.ProductionDetail {
		productDetail := productDetailMap[int(item.ProductDetailFKID)]
		product := productMap[productDetail.ProductFkid]
		if productDetail.PriceBuy == 0 {
			productDetail.PriceBuy = 1
		}
		log.Info("%s, %v x %v = %v ", product.Name, productDetail.PriceBuy, item.Qty, float64(productDetail.PriceBuy)*float64(item.Qty))
		if item.DetailType == model.ProductionTypeIngredient {
			if account, ok := accountSettingMap[int(item.ProductFKID)]; ok {
				journals = append(journals, domain.Journal{
					AccountCode:         account.InventoryCode,
					AccountName:         account.InventoryName,
					Type:                "K",
					Nominal:             (float64(productDetail.PriceBuy) * float64(item.Qty)),
					AccountCategoryFkid: 17,
					Description:         product.Name,
				})
			} else {
				totalIngredientLeft += float64(productDetail.PriceBuy) * float64(item.Qty)
			}
		}
	}

	productDetailPrimary := productDetailMap[int(data.Production.ProductDetailFKID)]
	if productDetailPrimary.PriceBuy == 0 {
		productDetailPrimary.PriceBuy = 1
	}
	log.Info(">> Primary: %v x %v = %v", productDetailPrimary.PriceBuy, data.Production.QtyPrimary, float64(productDetailPrimary.PriceBuy)*float64(data.Production.QtyPrimary))
	if account, ok := accountSettingMap[int(data.Production.ProductFkid)]; ok {
		product := productMap[productDetailPrimary.ProductFkid]
		journals = append(journals, domain.Journal{
			AccountCode:         account.InventoryCode,
			AccountName:         account.InventoryName,
			Type:                "K",
			Nominal:             (float64(productDetailPrimary.PriceBuy) * float64(data.Production.QtyPrimary)),
			AccountCategoryFkid: 17,
			Description:         product.Name,
		})
	} else {
		totalIngredientLeft += float64(productDetailPrimary.PriceBuy) * float64(data.Production.QtyPrimary)
	}

	journals = append(journals, domain.Journal{
		AccountCode:         "1-10200",
		AccountName:         "Persediaan Barang",
		Type:                "K",
		Nominal:             totalIngredientLeft,
		AccountCategoryFkid: 17,
	})

	totalCredit := float64(0)
	for _, journal := range journals {
		if journal.Type == "K" {
			totalCredit += journal.Nominal
		}
	}

	totalResidual := float64(0)
	for _, item := range data.ProductionDetail {
		if item.DetailType == model.ProductionTypeResidual {
			product := productDetailMap[int(item.ProductDetailFKID)]
			if product.PriceBuy == 0 {
				product.PriceBuy = 1
			}
			totalResidual += float64(product.PriceBuy) * float64(item.Qty)
		}
	}

	totalEndProduct := float64(0)
	for _, item := range data.ProductionDetail {
		if item.DetailType == model.ProductionTypeEndProduct {
			product := productDetailMap[int(item.ProductDetailFKID)]
			if product.PriceBuy == 0 {
				product.PriceBuy = 1
			}
			totalEndProduct += float64(product.PriceBuy) * float64(item.Qty)
		}
	}

	fmt.Println("total credit: ", totalCredit, "total residu: ", totalResidual, "totalEndProduct", totalEndProduct)
	totalEndProductLeft := float64(0)
	for _, item := range data.ProductionDetail {
		productDetail := productDetailMap[int(item.ProductDetailFKID)]
		product := productMap[productDetail.ProductFkid]
		if item.DetailType == model.ProductionTypeEndProduct || item.DetailType == model.ProductionTypeResidual {
			if productDetail.PriceBuy == 0 {
				productDetail.PriceBuy = 1
			}
			total := (float64(productDetail.PriceBuy) * float64(item.Qty) / totalEndProduct) * (totalCredit - totalResidual)
			if item.DetailType == model.ProductionTypeResidual {
				total = float64(productDetail.PriceBuy) * float64(item.Qty)
				log.Info("%v >> %v (%v) | %v x %v = %v", product.Name, total, item.DetailType, productDetail.PriceBuy, item.Qty, total)
			} else {
				log.Info("%v >> (%v) | %v x %v / %v * %v - %v = %v", product.Name, item.DetailType, productDetail.PriceBuy, item.Qty, totalEndProduct, totalCredit, totalResidual, total)
			}
			if account, ok := accountSettingMap[int(item.ProductFKID)]; ok {
				journals = append(journals, domain.Journal{
					AccountCode:         account.InventoryCode,
					AccountName:         account.InventoryName,
					Type:                "D",
					Nominal:             number.Round(total),
					AccountCategoryFkid: 17,
					Description:         product.Name,
				})
			} else {
				totalEndProductLeft += total //if product has no custom account, group them into one
			}
		}
	}

	fmt.Println(">>>", totalEndProductLeft)
	if totalEndProductLeft > 0 {
		journals = append(journals, domain.Journal{
			AccountCode:         "1-10200",
			AccountName:         "Persediaan Barang",
			Type:                "D",
			Nominal:             number.Round(totalEndProductLeft),
			AccountCategoryFkid: 17,
		})
	}

	transType := 9 // 9: production
	adminFkid := int(data.Production.AdminFKID)
	outletFkid := int(data.Production.OutletFKID)
	transId := strconv.Itoa(int(data.Production.ProductionID))
	transCreated := data.Production.DataCreated

	for i := range journals {
		journals[i].TransType = transType
		journals[i].AdminFkid = adminFkid
		journals[i].OutletFkid = outletFkid
		journals[i].TransId = transId
		journals[i].TransCreated = transCreated
	}

	return journals, nil
}

// production: production process, producing a new item
func (j journalUseCase) generateJournalProductionDebt(data productionData) ([]domain.Journal, error) {
	journals := make([]domain.Journal, 0)
	//for now, this journal is not created!
	if os.Getenv("ENV") != "localhost" {
		return journals, nil
	}
	totalCost := 0
	totalCostCustomAcccount := 0
	for _, cost := range data.ProductionCost {
		totalCost += cost.Nominal

		if account, ok := data.AccountDefaultMap[fmt.Sprintf("prc_category#%v", cost.PurchaseReportCategoryFKID)]; ok {
			journals = append(journals, domain.Journal{
				AccountCode:         account.Code,
				AccountName:         account.Name,
				Type:                "D",
				Nominal:             float64(cost.Nominal),
				AccountCategoryFkid: account.AccountCategoryFkid,
			})
			totalCostCustomAcccount += cost.Nominal
		}
	}

	journals = append(journals, domain.Journal{
		AccountCode:         "8-80999",
		AccountName:         "Beban Lain - lain",
		Type:                "D",
		Nominal:             float64(totalCost - totalCostCustomAcccount),
		AccountCategoryFkid: 17,
	})

	journals = append(journals, domain.Journal{
		AccountCode:         "2-20200",
		AccountName:         "Hutang Lain Lain",
		Type:                "K",
		Nominal:             float64(totalCost),
		AccountCategoryFkid: 17,
	})

	transType := 9 // 9: production
	adminFkid := int(data.Production.AdminFKID)
	outletFkid := int(data.Production.OutletFKID)
	transId := strconv.Itoa(int(data.Production.ProductionID))
	transCreated := data.Production.DataCreated

	for i := range journals {
		journals[i].TransType = transType
		journals[i].AdminFkid = adminFkid
		journals[i].OutletFkid = outletFkid
		journals[i].TransId = transId
		journals[i].TransCreated = transCreated
	}
	return journals, nil
}

func (j journalUseCase) getProductionData(productionId int) (productionData, error) {
	var result productionData
	var err error

	result.Production, err = j.repo.FetchProduction(productionId)
	if log.IfError(err) {
		return productionData{}, err
	}

	if result.Production.ProductionID == 0 {
		fmt.Println("no production with id: ", productionId)
		return productionData{}, &exceptions.ErrorCode{Code: 404, Message: fmt.Sprintf("no production with id %v", productionId)}
	}

	if !j.isFinanceActive(int(result.Production.AdminFKID)) {
		return productionData{}, fmt.Errorf("finance billing not active")
	}

	accountDefaults, err := j.repo.FetchAccountDefault(int(result.Production.AdminFKID))
	log.IfError(err)

	result.AccountDefaultMap = make(map[string]domain.AccountDefault, len(accountDefaults))
	for _, account := range accountDefaults {
		result.AccountDefaultMap[account.IdxKey] = account
	}

	//fetch the production_detail from database
	result.ProductionDetail, err = j.repo.FetchProductionDetail(productionId)
	if log.IfError(err) {
		return productionData{}, err
	}

	if len(result.ProductionDetail) == 0 {
		log.IfError(fmt.Errorf("production not found: %v", productionId))
		return productionData{}, fmt.Errorf("production not found: %v", productionId)
	}

	//fetch production_cost
	result.ProductionCost, err = j.repo.FetchProductionCost(productionId)
	if log.IfError(err) {
		return productionData{}, err
	}

	categoryIds := make([]int, 0)
	for _, cost := range result.ProductionCost {
		categoryIds = append(categoryIds, int(cost.PurchaseReportCategoryFKID))
	}

	if len(categoryIds) > 0 {
		result.PurcahseReportCategory, err = j.repoProduct.FetchPurchaseReportCategory(categoryIds)
		log.IfError(err)
	}

	return result, nil
}
