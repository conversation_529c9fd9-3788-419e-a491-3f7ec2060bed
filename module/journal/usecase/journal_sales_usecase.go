package usecase

import (
	"fmt"
	"math"
	"strings"

	"gitlab.com/uniqdev/backend/service-finance/domain"
)

func (j journalUseCase) ReceiveSalesHppUpdate(salesId string) {
	//NOTE: no need to delete, on insert in repo, it will delete the existing journal
	// fmt.Printf("deleting jurnal umum of 'sales' with id %s \n", salesId)
	// err := j.repo.DeleteJournal("sales", salesId)
	// log.IfError(err)
	j.ReceiveNewSales(salesId)
}

func (j journalUseCase) ReceiveNewSales(salesId string) {
	chanAdmin := make(chan domain.Admin)
	go j.repo.GetAdminBySalesId(chanAdmin, salesId)

	admin := <-chanAdmin
	if !j.isFinanceActive(admin.AdminID) {
		return
	}

	//payment
	chanPayment := make(chan []domain.SalesWithPayment)
	go j.repo.GetPaymentAndSales(chanPayment, salesId)

	//taxes
	chanTax := make(chan []domain.TaxWithGratuity)
	go j.repo.GetTax(chanTax, salesId)

	//salesDetails or sales detail
	chanSalesDetail := make(chan []domain.SalesDetail)
	go j.repo.GetSalesDetail(chanSalesDetail, salesId)

	//salesBreakdown
	chanSalesBreakdown := make(chan []domain.SalesBreakdown)
	go j.repo.GetSalesBreakdown(chanSalesBreakdown, salesId)

	//promotions
	chanTotalPromotion := make(chan int)
	go j.repo.GetTotalPromotion(chanTotalPromotion, salesId)

	chanSales := make(chan domain.Sales)
	go j.repo.GetSales(chanSales, salesId)

	chanRefund := make(chan domain.SalesRefund)
	go j.repo.GetSalesRefund(chanRefund, salesId)

	salesAndPayment := <-chanPayment
	tax := <-chanTax
	salesDetails := <-chanSalesDetail
	salesDetailBreakdowns := <-chanSalesBreakdown
	totalPromo := <-chanTotalPromotion
	sales := <-chanSales
	salesRefund := <-chanRefund

	productIds := make([]int, 0)
	for _, detail := range salesDetails {
		productIds = append(productIds, detail.ProductFkid)
	}

	for _, breakdown := range salesDetailBreakdowns {
		productIds = append(productIds, breakdown.ProductFkid)
	}

	products, _ := j.repo.FetchProduct(productIds...)
	productMap := make(map[int]domain.Product)
	for _, row := range products {
		productMap[row.ProductId] = row
	}

	// fmt.Println("[DISC] promotion: ", totalPromo)
	if len(salesAndPayment) == 0 {
		fmt.Printf("no sales with id: '%s'\n", salesId)
		return
	}

	totalPromo += salesAndPayment[0].Discount + salesAndPayment[0].Voucher
	taxTotal := 0

	for _, t := range tax {
		if t.Category == "discount" {
			totalPromo += t.Total
		} else {
			taxTotal += t.Total
		}
	}

	transactionPayment := make([]domain.TransactionPayment, 0)
	for _, sp := range salesAndPayment {
		transactionPayment = append(transactionPayment, domain.TransactionPayment{
			Total:    sp.Total,
			Method:   strings.Replace(strings.ToLower(sp.Method), " ", "_", -1),
			BankName: sp.BankName,
			BankId:   sp.BankId,
		})
	}

	totalSubTotal := 0
	transactionItem := make([]domain.TransactionItem, 0)
	for _, sd := range salesDetails {
		//if the item has breakdown, hpp should be calculated from breakdown
		isHasBreakdown := false
		for _, breakdown := range salesDetailBreakdowns {
			if breakdown.SalesDetailFkid == sd.SalesDetailID {
				isHasBreakdown = true

				transactionItem = append(transactionItem, domain.TransactionItem{
					PriceBuy:        (breakdown.QtyTotal * float64(breakdown.PriceBuy)),
					ProductId:       breakdown.ProductFkid,
					ProductDetailId: breakdown.ProductDetailFkid,
					Product:         productMap[breakdown.ProductFkid],
					IsBreakdown:     true,
				})
				break
			}
		}

		priceBuy := sd.PriceBuy * float64(sd.Qty)
		if isHasBreakdown {
			priceBuy = 0
		}

		// fmt.Println("====== PriceBuy", sd.PriceBuy, "Qty", sd.Qty, "---> ", priceBuy)
		transactionItem = append(transactionItem, domain.TransactionItem{
			PriceSell:       sd.Price,
			PriceBuy:        priceBuy,
			ProductId:       sd.ProductFkid,
			ProductDetailId: sd.ProductDetailFkid,
			Subtotal:        sd.SubTotal,
			Discount:        sd.Discount,
			Product:         productMap[sd.ProductFkid],
		})

		totalPromo += sd.Discount
		totalSubTotal += sd.SubTotal + sd.Discount
	}

	//penyesuaian pembulatan
	beforeRound := totalSubTotal - totalPromo + taxTotal
	diff := salesAndPayment[0].GrandTotal - beforeRound
	fmt.Printf("diff -> %d - (%d - %d + %d)  = %d\n", salesAndPayment[0].GrandTotal, totalSubTotal, totalPromo, taxTotal, diff)
	if totalPromo > (totalSubTotal + taxTotal) {
		fmt.Println("Total Promo is greater than total subtotal + tax")
		if salesAndPayment[0].GrandTotal == 0 { //grand total 0, means all total covered by promotion
			totalPromo = totalSubTotal + taxTotal
			fmt.Println("adjusting total promo to: ", totalPromo)
		}
	}
	if diff > 0 && diff <= 100 {
		fmt.Println("[PEMBULATAN] diff: ", diff)
		idx := 0 //hold index item with subtotal changed
		//fmt.Println("item --> ", cast.ToJson(transactionItem))
		totalSubTotalNew := 0
		for i, item := range transactionItem {
			if item.Subtotal+item.Discount == 0 {
				continue
			}
			idx = i
			fmt.Println("subtotal: ", item.Subtotal)
			deviden := int(math.Round((float64(diff) / float64(totalSubTotal)) * float64(item.Subtotal+item.Discount)))
			//fmt.Printf("%d: %d", item.Subtotal+item.Discount, deviden)
			fmt.Printf("\n%d / %d * %d = %d\n", diff, totalSubTotal, item.Subtotal+item.Discount, deviden)
			transactionItem[i].Subtotal = item.Subtotal + deviden
			totalSubTotalNew += transactionItem[i].Subtotal + item.Discount
			fmt.Println("subtotal now: ", transactionItem[i].Subtotal)
		}
		//fmt.Println("item now --> ", cast.ToJson(transactionItem))

		//final check, if still still found, add it to any item
		beforeRound = totalSubTotalNew - totalPromo + taxTotal
		diff = salesAndPayment[0].GrandTotal - beforeRound
		fmt.Println("diff now: ", diff, "idx will change: ", idx)
		if diff != 0 {
			transactionItem[idx].Subtotal += diff
			fmt.Println(transactionItem[idx].Subtotal)
		}
	}

	if strings.ToLower(salesAndPayment[0].Status) == "refund" {
		// totalPromo = 0
	}

	transId := salesAndPayment[0].SalesID
	transType := domain.TransTypeSales

	//if refund, add refundId as suffix, this will prevent journal of sales to be deleted
	if salesRefund.SalesRefundID > 0 {
		transId = fmt.Sprintf("%v.%v", transId, salesRefund.SalesRefundID)
		transType = domain.TransTypeSalesRefund
	}

	journal := domain.JournalTransaction{
		AdminId:             admin.AdminID,
		OutletId:            salesAndPayment[0].OutletFkid,
		TransactionType:     transType,
		TransactionId:       transId,
		TransactionTime:     salesAndPayment[0].Sales.TimeCreated,
		DiscountTotal:       totalPromo,
		TaxTotal:            taxTotal,
		Item:                transactionItem,
		Payment:             transactionPayment,
		TransactionStatus:   strings.ToLower(salesAndPayment[0].Status),
		DiscountTransaction: salesAndPayment[0].Discount + salesAndPayment[0].Voucher,
		TaxDetail:           tax,
		Description:         sales.DisplayNota,
	}

	j.addTransactionToJournal(journal)
}

func (j journalUseCase) ReceivePiutangPayment(paymentId int) {
	salesPayment := j.repo.GetSalesPaymentById(paymentId)

	chanAdmin := make(chan domain.OutletAndAdmin)
	go j.repo.GetOutletAndAdminBySalesId(chanAdmin, salesPayment.SalesFkid)

	admin := <-chanAdmin

	transactionPayment := make([]domain.TransactionPayment, 0)
	transactionPayment = append(transactionPayment, domain.TransactionPayment{
		Total:    salesPayment.Total,
		Method:   strings.Replace(strings.ToLower(salesPayment.Method), " ", "_", -1),
		BankName: salesPayment.BankName,
	})

	journal := domain.JournalTransaction{
		AdminId:         admin.AdminID,
		OutletId:        admin.OutletID,
		TransactionType: domain.TransTypeSales,
		TransactionId:   salesPayment.SalesFkid,
		TransactionTime: salesPayment.TimeCreated,
		Payment:         transactionPayment,
		PiutangCredit:   salesPayment.Total,
	}
	j.addTransactionToJournal(journal)
}
