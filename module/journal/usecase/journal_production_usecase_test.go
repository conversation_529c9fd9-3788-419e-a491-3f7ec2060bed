package usecase

import (
	"encoding/json"
	"fmt"
	"os"
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/uniqdev/backend/service-finance/domain"
	"gitlab.com/uniqdev/backend/service-finance/model"
	"gitlab.com/uniqdev/backend/service-finance/module/journal"
	journalMock "gitlab.com/uniqdev/backend/service-finance/module/journal/mocks"
	"gitlab.com/uniqdev/backend/service-finance/module/product"
	productMock "gitlab.com/uniqdev/backend/service-finance/module/product/mocks"
)

type TestData struct {
	Production             model.ProductionEntity
	DefaultAccount         []domain.AccountDefault
	DefaultAccountSet      []domain.AccountDefault
	ProductionCost         []model.ProductionCostEntity
	ProductionDetail       []model.ProductionDetailEntity
	Subscriptions          []model.SystemSubscribe
	ProductDetail          []model.ProductDetailEntity
	AccountSetting         []domain.AccountSetting
	PurchaseReportCategory []model.PurchaseReportCategoryEntity
	Product                []domain.Product
}

func SetupTestData() (TestData, error) {
	var testData TestData
	var err error

	jsonProduction := `{"production_id":1,"itembreakdown_fkid":74,"product_detail_fkid":8430,"outlet_fkid":29,"qty_recipe":0,"qty_primary":1,"employee_fkid":null,"admin_fkid":1,"date_input":"2023-06-30","data_created":*************,"data_modified":*************,"data_delete_at":null,"transferred":0}`
	err = json.Unmarshal([]byte(jsonProduction), &testData.Production)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonProduction: %v", err)
	}

	jsonDefaultAccount := `[{"code":"1-10002","name":"Rekening Bank","idx_key":"payment#35","account_category_fkid":3,"type":"payment","type_fkid":35},{"code":"4-40100","name":"Diskon Penjualan","idx_key":"gratuity#48","account_category_fkid":13,"type":"gratuity","type_fkid":48}]`
	err = json.Unmarshal([]byte(jsonDefaultAccount), &testData.DefaultAccount)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonDefaultAccount: %v", err)
	}

	jsonDefaultAccountSet := `[{"code":"6-60200","name":"Donasi","idx_key":"prc_category#240","account_category_fkid":16,"type":"prc_category","type_fkid":240},{"code":"6-60101","name":"Gaji","idx_key":"prc_category#225","account_category_fkid":16,"type":"prc_category","type_fkid":225}]`
	err = json.Unmarshal([]byte(jsonDefaultAccountSet), &testData.DefaultAccountSet)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonDefaultAccountSet: %v", err)
	}

	jsonProductionCost := `[{"id":1,"production_fkid":1,"purchase_report_category_fkid":225,"nominal":10000},{"id":2,"production_fkid":1,"purchase_report_category_fkid":39,"nominal":17000},{"id":3,"production_fkid":1,"purchase_report_category_fkid":234,"nominal":30000}]`
	err = json.Unmarshal([]byte(jsonProductionCost), &testData.ProductionCost)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonProductionCost: %v", err)
	}

	jsonProductionDetail := `[{"productiondetail_id":487,"production_fkid":118,"product_fkid":2508,"product_detail_fkid":8429,"qty":4,"detail_type":"endproduct","data_created":*************,"data_modified":*************},{"productiondetail_id":486,"production_fkid":118,"product_fkid":4977,"product_detail_fkid":15629,"qty":2,"detail_type":"endproduct","data_created":*************,"data_modified":*************},{"productiondetail_id":485,"production_fkid":118,"product_fkid":4975,"product_detail_fkid":15628,"qty":2,"detail_type":"endproduct","data_created":*************,"data_modified":*************},{"productiondetail_id":484,"production_fkid":118,"product_fkid":4974,"product_detail_fkid":15627,"qty":1,"detail_type":"endproduct","data_created":*************,"data_modified":*************},{"productiondetail_id":483,"production_fkid":118,"product_fkid":4985,"product_detail_fkid":15700,"qty":2,"detail_type":"ingredient","data_created":*************,"data_modified":*************},{"productiondetail_id":482,"production_fkid":118,"product_fkid":4986,"product_detail_fkid":15348,"qty":2,"detail_type":"ingredient","data_created":*************,"data_modified":*************},{"productiondetail_id":481,"production_fkid":118,"product_fkid":2508,"product_detail_fkid":8429,"qty":1,"detail_type":"ingredient","data_created":*************,"data_modified":*************}]`
	err = json.Unmarshal([]byte(jsonProductionDetail), &testData.ProductionDetail)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonProductionDetail: %v", err)
	}

	jsonProductDetail := `[{"product_detail_id":8429,"product_fkid":2508,"outlet_fkid":29,"price_buy":10000,"price_sell":15000},{"product_detail_id":15348,"product_fkid":4986,"outlet_fkid":29,"price_buy":700,"price_sell":1000},{"product_detail_id":15700,"product_fkid":4985,"outlet_fkid":29,"price_buy":0,"price_sell":0},{"product_detail_id":15627,"product_fkid":4974,"outlet_fkid":29,"price_buy":2500,"price_sell":13500},{"product_detail_id":15628,"product_fkid":4975,"outlet_fkid":29,"price_buy":6000,"price_sell":12000},{"product_detail_id":15629,"product_fkid":4977,"outlet_fkid":29,"price_buy":5000,"price_sell":10000}]`
	err = json.Unmarshal([]byte(jsonProductDetail), &testData.ProductDetail)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonProductDetail: %v", err)
	}

	jsonAccountSetting := `[{"product_detail_fkid":8429,"account_debit":"5-50000","debit_name":"Beban Pokok Pendapatan","account_kredit":"4-40000","credit_name":"Pendapatan","inventory_code":"1-10200","inventory_name":"Persediaan Barang","debit_category_id":15,"credit_category_id":13,"inventory_category_id":4}]`
	err = json.Unmarshal([]byte(jsonAccountSetting), &testData.AccountSetting)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonAccountSetting: %v", err)
	}

	testData.Subscriptions = []model.SystemSubscribe{{ID: 1}}

	return testData, nil
}

func SetupTestData2() (TestData, error) {
	var testData TestData
	var err error

	jsonProduction := `{"production_id":132,"itembreakdown_fkid":85,"product_detail_fkid":37182,"outlet_fkid":628,"qty_recipe":0,"qty_primary":2,"employee_fkid":null,"admin_fkid":7,"date_input":"2023-07-04","data_created":*************,"data_modified":*************,"data_delete_at":null,"transferred":0}`
	err = json.Unmarshal([]byte(jsonProduction), &testData.Production)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonProduction: %v", err)
	}

	jsonDefaultAccount := `[{"code":"1-10002","name":"Rekening Bank","idx_key":"payment#35","account_category_fkid":3,"type":"payment","type_fkid":35},{"code":"4-40100","name":"Diskon Penjualan","idx_key":"gratuity#48","account_category_fkid":13,"type":"gratuity","type_fkid":48}]`
	err = json.Unmarshal([]byte(jsonDefaultAccount), &testData.DefaultAccount)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonDefaultAccount: %v", err)
	}

	jsonDefaultAccountSet := `[{"code":"6-60200","name":"Donasi","idx_key":"prc_category#240","account_category_fkid":16,"type":"prc_category","type_fkid":240},{"code":"6-60101","name":"Gaji","idx_key":"prc_category#225","account_category_fkid":16,"type":"prc_category","type_fkid":225}]`
	err = json.Unmarshal([]byte(jsonDefaultAccountSet), &testData.DefaultAccountSet)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonDefaultAccountSet: %v", err)
	}

	jsonProductionCost := `[{"id":19,"production_fkid":132,"purchase_report_category_fkid":238,"nominal":5000}]`
	err = json.Unmarshal([]byte(jsonProductionCost), &testData.ProductionCost)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonProductionCost: %v", err)
	}

	jsonProductionDetail := `[{"productiondetail_id":579,"production_fkid":132,"product_fkid":9762,"product_detail_fkid":37180,"qty":4,"detail_type":"ingredient","data_created":*************,"data_modified":*************},{"productiondetail_id":580,"production_fkid":132,"product_fkid":9765,"product_detail_fkid":37186,"qty":20,"detail_type":"ingredient","data_created":*************,"data_modified":*************},{"productiondetail_id":581,"production_fkid":132,"product_fkid":9766,"product_detail_fkid":37188,"qty":20,"detail_type":"ingredient","data_created":*************,"data_modified":*************},{"productiondetail_id":582,"production_fkid":132,"product_fkid":9767,"product_detail_fkid":37190,"qty":16,"detail_type":"ingredient","data_created":*************,"data_modified":*************},{"productiondetail_id":583,"production_fkid":132,"product_fkid":9769,"product_detail_fkid":37194,"qty":300,"detail_type":"ingredient","data_created":*************,"data_modified":*************},{"productiondetail_id":584,"production_fkid":132,"product_fkid":9770,"product_detail_fkid":37196,"qty":500,"detail_type":"ingredient","data_created":*************,"data_modified":*************},{"productiondetail_id":585,"production_fkid":132,"product_fkid":9768,"product_detail_fkid":37192,"qty":300,"detail_type":"ingredient","data_created":*************,"data_modified":*************},{"productiondetail_id":586,"production_fkid":132,"product_fkid":9761,"product_detail_fkid":37179,"qty":3,"detail_type":"endproduct","data_created":*************,"data_modified":*************},{"productiondetail_id":587,"production_fkid":132,"product_fkid":9762,"product_detail_fkid":37180,"qty":0.06,"detail_type":"residual","data_created":*************,"data_modified":*************},{"productiondetail_id":588,"production_fkid":132,"product_fkid":9763,"product_detail_fkid":37182,"qty":0.06,"detail_type":"residual","data_created":*************,"data_modified":*************}]`
	err = json.Unmarshal([]byte(jsonProductionDetail), &testData.ProductionDetail)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonProductionDetail: %v", err)
	}

	jsonProductDetail := `[{"product_detail_id":37180,"product_fkid":9762,"outlet_fkid":628,"price_buy_start":13000,"price_buy":13000,"price_sell":15000,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":*************,"variant_fkid":null,"stock":"available","stock_qty":2,"data_status":"on"},{"product_detail_id":37186,"product_fkid":9765,"outlet_fkid":628,"price_buy_start":150,"price_buy":150,"price_sell":200,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":*************,"variant_fkid":null,"stock":"available","stock_qty":10,"data_status":"on"},{"product_detail_id":37188,"product_fkid":9766,"outlet_fkid":628,"price_buy_start":250,"price_buy":250,"price_sell":300,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":*************,"variant_fkid":null,"stock":"available","stock_qty":10,"data_status":"on"},{"product_detail_id":37190,"product_fkid":9767,"outlet_fkid":628,"price_buy_start":350,"price_buy":350,"price_sell":450,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":*************,"variant_fkid":null,"stock":"available","stock_qty":18,"data_status":"on"},{"product_detail_id":37194,"product_fkid":9769,"outlet_fkid":628,"price_buy_start":87000,"price_buy":87,"price_sell":90000,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":*************,"variant_fkid":null,"stock":"available","stock_qty":4200,"data_status":"on"},{"product_detail_id":37196,"product_fkid":9770,"outlet_fkid":628,"price_buy_start":35000,"price_buy":35,"price_sell":40000,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":*************,"variant_fkid":null,"stock":"available","stock_qty":4000,"data_status":"on"},{"product_detail_id":37192,"product_fkid":9768,"outlet_fkid":628,"price_buy_start":85000,"price_buy":85,"price_sell":90000,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":*************,"variant_fkid":null,"stock":"available","stock_qty":4400,"data_status":"on"},{"product_detail_id":37179,"product_fkid":9761,"outlet_fkid":628,"price_buy_start":0,"price_buy":0,"price_sell":650000,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":*************,"variant_fkid":null,"stock":"available","stock_qty":7,"data_status":"on"},{"product_detail_id":37182,"product_fkid":9763,"outlet_fkid":628,"price_buy_start":25000,"price_buy":25000,"price_sell":28000,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":*************,"variant_fkid":null,"stock":"available","stock_qty":6,"data_status":"on"}]`
	err = json.Unmarshal([]byte(jsonProductDetail), &testData.ProductDetail)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonProductDetail: %v", err)
	}

	jsonAccountSetting := `[{"product_detail_fkid":8429,"account_debit":"5-50000","debit_name":"Beban Pokok Pendapatan","account_kredit":"4-40000","credit_name":"Pendapatan","inventory_code":"1-10200","inventory_name":"Persediaan Barang","debit_category_id":15,"credit_category_id":13,"inventory_category_id":4}]`
	err = json.Unmarshal([]byte(jsonAccountSetting), &testData.AccountSetting)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonAccountSetting: %v", err)
	}

	jsonPurchaseReportCategory := `[{"purchase_report_category_id":238,"name":"BBM","is_operationalcost":1,"admin_fkid":7,"data_created":"2023-02-16 03:24:22","data_modified":"2023-02-16 03:24:22","data_status":"on"}]`
	err = json.Unmarshal([]byte(jsonPurchaseReportCategory), &testData.PurchaseReportCategory)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonPurchaseReportCategory: %v", err)
	}

	jsonProduct := `[{"product_id":9762,"product_detail_id":37180,"name":"pine board 1 cm"},{"product_id":9765,"product_detail_id":37186,"name":"screw 2 cm"},{"product_id":9766,"product_detail_id":37188,"name":"screw 5 cm"},{"product_id":9767,"product_detail_id":37190,"name":"screw 8 cm"},{"product_id":9769,"product_detail_id":37194,"name":"wood coating"},{"product_id":9770,"product_detail_id":37196,"name":"wood filler"},{"product_id":9768,"product_detail_id":37192,"name":"Wood Stain Propan"},{"product_id":9761,"product_detail_id":37179,"name":"Your Dad's Coffee Table"},{"product_id":9762,"product_detail_id":37180,"name":"pine board 1 cm"},{"product_id":9763,"product_detail_id":37182,"name":"pine board 2 cm"}]`
	err = json.Unmarshal([]byte(jsonProduct), &testData.Product)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonProduct: %v", err)
	}

	testData.Subscriptions = []model.SystemSubscribe{{ID: 1}}

	return testData, nil
}

func Test_journalUseCase_generateJournalProductionDebt(t *testing.T) {
	os.Setenv("ENV", "development")

	testData, err := SetupTestData()
	assert.NoError(t, err)

	jsonJournalNoDefaultAccount := `[{"trans_type":9,"trans_id":"1","trans_created":*************,"account_code":"8-80999","account_name":"Beban Lain - lain","type":"D","nominal":57000,"admin_fkid":1,"outlet_fkid":29,"account_category_fkid":17},{"trans_type":9,"trans_id":"1","trans_created":*************,"account_code":"2-20200","account_name":"Hutang Lain Lain","type":"K","nominal":57000,"admin_fkid":1,"outlet_fkid":29,"account_category_fkid":17}]`
	jsonJournalWDefaultAccount := `[{"trans_type":9,"trans_id":"1","trans_created":*************,"account_code":"6-60101","account_name":"Gaji","type":"D","nominal":10000,"admin_fkid":1,"outlet_fkid":29,"account_category_fkid":16},{"trans_type":9,"trans_id":"1","trans_created":*************,"account_code":"8-80999","account_name":"Beban Lain - lain","type":"D","nominal":47000,"admin_fkid":1,"outlet_fkid":29,"account_category_fkid":17},{"trans_type":9,"trans_id":"1","trans_created":*************,"account_code":"2-20200","account_name":"Hutang Lain Lain","type":"K","nominal":57000,"admin_fkid":1,"outlet_fkid":29,"account_category_fkid":17}]`

	var journalNoDefaultAccount []domain.Journal
	assert.NoError(t, json.Unmarshal([]byte(jsonJournalNoDefaultAccount), &journalNoDefaultAccount))

	var journalWithDefaultAccount []domain.Journal
	assert.NoError(t, json.Unmarshal([]byte(jsonJournalWDefaultAccount), &journalWithDefaultAccount))

	prodIdMocks := make([]interface{}, 0)
	for _, prod := range testData.ProductionDetail {
		fmt.Print(prod.ProductDetailFKID)
		prodIdMocks = append(prodIdMocks, mock.Anything)
	}
	prodIdMocks = append(prodIdMocks, testData.Production.ProductDetailFKID)

	productionId := 1
	mockRepo := new(journalMock.Repository)
	mockProductRepo := new(productMock.Repository)

	//set mocking returns
	mockRepo.On("FetchProduction", productionId).Return(testData.Production, nil)
	mockRepo.On("FetchUserSubscription", mock.Anything, mock.Anything).Return(testData.Subscriptions, nil)
	mockRepo.On("FetchProductionDetail", productionId).Return(testData.ProductionDetail, nil)
	mockRepo.On("FetchProductionCost", productionId).Return(testData.ProductionCost, nil)
	mockProductRepo.On("FetchProductDetail", prodIdMocks...).Return(testData.ProductDetail, nil)

	type fields struct {
		repo        journal.Repository
		repoProduct product.Repository
	}
	type args struct {
		productionId int
	}
	type mockReturns struct {
		AccountDefault []domain.AccountDefault
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		returns mockReturns
		want    []domain.Journal
		wantErr bool
	}{
		{"noDefaultAccount", fields{mockRepo, mockProductRepo}, args{1}, mockReturns{testData.DefaultAccount}, journalNoDefaultAccount, false},
		{"withDefaultAccount", fields{mockRepo, mockProductRepo}, args{1}, mockReturns{testData.DefaultAccountSet}, journalWithDefaultAccount, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			j := journalUseCase{
				repo:        tt.fields.repo,
				repoProduct: tt.fields.repoProduct,
			}

			mockCall := mockRepo.On("FetchAccountDefault", mock.Anything).Return(tt.returns.AccountDefault, nil)
			defer mockCall.Unset()

			productionData, err := j.getProductionData(tt.args.productionId)
			if (err != nil) != tt.wantErr {
				t.Errorf("journalUseCase.generateJournalProduction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			got, err := j.generateJournalProductionDebt(productionData)
			// got, err := j.generateJournalProductionItem(productionData)
			if (err != nil) != tt.wantErr {
				t.Errorf("journalUseCase.generateJournalProduction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			jsonJournal, _ := json.Marshal(got)
			fmt.Println(tt.name, ", journal :", string(jsonJournal))

			valid := isValidJournal(got)
			fmt.Println("isValid: ", valid)
			assert.True(t, valid)

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("journalUseCase.generateJournalProduction() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_journalUseCase_generateJournalProductionNotFound(t *testing.T) {
	mockRepo := new(journalMock.Repository)
	j := journalUseCase{
		repo: mockRepo,
	}

	var err2 error
	mockRepo.On("FetchProduction", mock.Anything).Return(model.ProductionEntity{}, err2)

	_, err := j.getProductionData(1)
	mockRepo.AssertCalled(t, "FetchProduction", 1)
	assert.Error(t, err)
}

func Test_journalUseCase_generateJournalProductionItem(t *testing.T) {
	os.Setenv("ENV", "development")

	testData, err := SetupTestData2()
	assert.NoError(t, err)

	productionId := 1
	mockRepo := new(journalMock.Repository)
	mockRepoProduct := new(productMock.Repository)

	prodIdMocks := anythings(len(testData.ProductionDetail) + 1)

	//set mocking returns
	mockRepo.On("FetchProduction", productionId).Return(testData.Production, nil)
	mockRepo.On("FetchUserSubscription", mock.Anything, mock.Anything).Return(testData.Subscriptions, nil)
	mockRepo.On("FetchProductionDetail", productionId).Return(testData.ProductionDetail, nil)
	mockRepo.On("FetchProductionCost", productionId).Return(testData.ProductionCost, nil)
	mockRepo.On("GetAccountByProduct", mock.Anything).Return(testData.AccountSetting)
	mockRepo.On("FetchProduct", anythings(len(prodIdMocks))...).Return(testData.Product, nil)
	mockRepoProduct.On("FetchPurchaseReportCategory", mock.Anything).Return(testData.PurchaseReportCategory, nil)
	mockRepoProduct.On("FetchProductDetail", prodIdMocks...).Return(testData.ProductDetail, nil)

	type fields struct {
		repo        journal.Repository
		repoProduct product.Repository
	}
	type mockReturns struct {
		AccountDefault []domain.AccountDefault
	}
	tests := []struct {
		name    string
		fields  fields
		returns mockReturns
		want    []domain.Journal
		wantErr bool
	}{
		{"default", fields{mockRepo, mockRepoProduct}, mockReturns{testData.DefaultAccount}, []domain.Journal{}, false},
		{"accountCustom", fields{mockRepo, mockRepoProduct}, mockReturns{testData.DefaultAccountSet}, []domain.Journal{}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			j := journalUseCase{
				repo:        tt.fields.repo,
				repoProduct: tt.fields.repoProduct,
			}

			mockRepo.On("FetchAccountDefault", mock.Anything).Return(tt.returns.AccountDefault, nil).Once()
			productionData, err := j.getProductionData(productionId)
			if (err != nil) != tt.wantErr {
				t.Errorf("journalUseCase.generateJournalProduction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			got, err := j.generateJournalProductionItem(productionData)
			if (err != nil) != tt.wantErr {
				t.Errorf("journalUseCase.generateJournalProductionItem() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("journalUseCase.generateJournalProduction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			jsonJournal, _ := json.Marshal(removeZeroJournal(got))
			fmt.Println(tt.name, ", journal :", string(jsonJournal))

			valid := isValidJournal(got)
			fmt.Println("isValid: ", valid)
			assert.True(t, valid)

			// if !reflect.DeepEqual(got, tt.want) {
			// 	t.Errorf("journalUseCase.generateJournalProductionItem() = %v, want %v", got, tt.want)
			// }
		})
	}
}

func anythings(size int) []interface{} {
	things := make([]interface{}, 0)
	for i := 0; i < size; i++ {
		things = append(things, mock.Anything)
	}
	return things
}
