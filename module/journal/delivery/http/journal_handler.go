package http

import (
	"encoding/base64"
	"encoding/json"
	"fmt"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/uniqdev/backend/service-finance/core/util/cast"
	"gitlab.com/uniqdev/backend/service-finance/module/journal"
)

type journalHandler struct {
	uc journal.JournalUseCase
}

func NewHttpJournalHandler(app *fiber.App, useCase journal.JournalUseCase) {
	handler := &journalHandler{useCase}
	app.Get("/module/journal", handler.Sample)

	app.Get("/test/trigger/:type/:id", handler.TestNewSales)

	app.Post("/journal/trigger/sales", handler.NewSales)
	app.Post("/journal/trigger/sales_piutang", handler.newSalesPiutang)

	app.Post("/journal/trigger/purchase", handler.newPurchase)

	app.Post("/journal/convert-trans-type", handler.convertTransType)

	app.Get("/module/tespost", handler.Sample)
}

// type User struct {
// 	Name string `json:"name" xml:"name" form:"name"`
// 	Age  string `json:"age" xml:"age" form:"age"`
// }

// var allData = []User{}

// func (h journalHandler) Tespost(c *fiber.Ctx) error {
// 	var data2 map[string]string

// 	var data []User
// 	fmt.Println("body --> ", string(c.Body()))
// 	if err := c.BodyParser(&data2); err != nil {
// 		return err
// 	}
// 	allData = append(allData, data...)
// data2["name"]

// 	x := "xxx"
// 	callme(&x)
// 	fmt.Println(x)

// 	h.uc.

// 	return c.JSON(allData)
// }

// func callme(x *string){

// 	*x = "oke"

// }

func (h journalHandler) Sample(c *fiber.Ctx) error {
	return c.SendString("this is sample of journal feature route")
}

func (h journalHandler) TestNewSales(c *fiber.Ctx) error {
	id := c.Params("id")
	transType := c.Params("type")
	if transType == "sales" {
		go h.uc.ReceiveNewSales(id)
	} else if transType == "piutang" {
		go h.uc.ReceivePiutangPayment(cast.ToInt(id))
	} else {
		return c.SendString("unknown type...")
	}
	return c.SendString(fmt.Sprintf("%s %s has been handled", transType, id))
}

func (h journalHandler) NewSales(c *fiber.Ctx) error {
	type bodySales struct {
		SalesId  string `json:"sales_id"`
		Status   string `json:"status"`
		OutletId int    `json:"outlet_id"`
		AdminId  int    `json:"admin_id"`
	}
	data, err := readPubSubMessage(c)
	fmt.Println("new sales --> ", string(data))

	var sales bodySales
	err = json.Unmarshal(data, &sales)
	if err != nil {
		fmt.Println("Unmarshal data sales err", err)
	}

	go h.uc.ReceiveNewSales(sales.SalesId)
	return c.SendStatus(201)
}

func (h journalHandler) newSalesPiutang(c *fiber.Ctx) error {
	data, err := readPubSubMessage(c)
	if err != nil {
		return err
	}
	fmt.Println("new piutang --> ", string(data))

	var piutang struct {
		SalesId   string `json:"sales_id"`
		PiutangId int    `json:"piutang_id"`
		PaymentId int    `json:"payment_id"`
		AdminId   int    `json:"admin_id"`
	}
	err = json.Unmarshal(data, &piutang)
	if err != nil {
		fmt.Println("Unmarshal data piutang err", err)
	}

	go h.uc.ReceivePiutangPayment(piutang.PaymentId)
	return c.SendStatus(201)
}

func (h journalHandler) newPurchase(c *fiber.Ctx) error {
	data, err := readPubSubMessage(c)
	if err != nil {
		fmt.Println("error")
	}
	fmt.Println("new purchase --> ", string(data))
	purchase := h.uc.GetPurchase(string(data))
	return c.JSON(purchase[0])
}

// func (h journalHandler) newPurchase(c *fiber.Ctx) error {
// 	data, err := readPubSubMessage(c)
// 	purchase := h.uc.GetPurchase(string(data))

// 	if err := c.JSON(&fiber.Map{
//         "success": true,
//         "message": "Product successfully created",
//         "product": purchase,
//       }); err != nil {
//         c.Status(500).JSON(&fiber.Map{
//             "success": false,
//             "message":  "Error creating product",
//           })
//         // return
//     }
// }

func readPubSubMessage(c *fiber.Ctx) ([]byte, error) {
	type body struct {
		Message struct {
			Attributes struct {
				Key string `json:"key"`
			} `json:"attributes"`
			Data      string `json:"data"`
			MessageID string `json:"messageId"`
		} `json:"message"`
		Subscription string `json:"subscription"`
	}

	var pubSubMsg body
	err := c.BodyParser(&pubSubMsg)
	if err != nil {
		return nil, err
	}

	data, err := base64.StdEncoding.DecodeString(pubSubMsg.Message.Data)
	if err != nil {
		fmt.Println("decode data from pubsub err", err)
	}
	return data, err
}

func (h journalHandler) convertTransType(c *fiber.Ctx) error {
	// Run the conversion process
	err := h.uc.ConvertTransType()
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"success": false,
			"message": fmt.Sprintf("Failed to convert trans_type: %v", err),
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Trans_type conversion completed successfully",
	})
}
