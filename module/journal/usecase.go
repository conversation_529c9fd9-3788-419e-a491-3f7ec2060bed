package journal

import "gitlab.com/uniqdev/backend/service-finance/domain"

type JournalUseCase interface {
	RunJob()
	RunInvalidJournal()
	CheckMissingJournals(params ...domain.MissingJournalParam)
	ReceiveNewSales(salesId string)
	ReceivePiutangPayment(paymentId int)
	ReceiveSalesHppUpdate(salesId string)
	ReceiveNewProduction(productionId int)
	GetPurchase(purchaseId string) []domain.DataPurchase
}
