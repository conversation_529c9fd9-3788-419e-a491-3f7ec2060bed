package journal

import (
	"gitlab.com/uniqdev/backend/service-finance/domain"
	"gitlab.com/uniqdev/backend/service-finance/model"
)

type Repository interface {
	GetPaymentAndSales(chanPayment chan []domain.SalesWithPayment, salesId string)
	GetSalesDetail(chanProducts chan []domain.SalesDetail, salesId string)
	GetSalesBreakdown(chanSalesBreakdown chan []domain.SalesBreakdown, salesId string)
	GetTotalPromotion(chanTotalPromotion chan int, salesId string)
	GetSalesPaymentById(paymentId int) domain.Payment
	GetAdminBySalesId(chanAdmin chan domain.Admin, salesId string)
	GetSales(chatSales chan domain.Sales, salesId string)
	GetOutletAndAdminBySalesId(chan domain.OutletAndAdmin, string)
	GetTax(chanTax chan []domain.TaxWithGratuity, salesId string)
	GetSalesRefund(chanRefund chan domain.SalesRefund, salesId string)

	InsertJournal(journal domain.Journal)
	InsertJournals(journal ...domain.Journal)
	DeleteJournal(transType string, transId string) error
	FetchJournalFailure() (*[]domain.JournalFailures, error)
	InsertJournalFailures(failures domain.JournalFailures)
	DeleteJournalFailure(transId string) error
	GetMissingJournals(startTime int64, endTime int64, adminId int) ([]string, error)

	FetchTransactionType(transactionType string) (int, error)

	GetDefaultAccount() []domain.CoaJournal
	GetAccountByProduct(productDetailIds []int) []domain.AccountSetting
	FetchAccountDefault(adminId int) ([]domain.AccountDefault, error)

	GetPurchaseById(purchaseId string) []domain.DataPurchase
	GetPurchaseDetail(purchaseId string) []domain.PurchaseDetail

	FetchProduct(productId ...int) ([]domain.Product, error)
	FetchUserSubscription(adminId int, serviceType string) ([]model.SystemSubscribe, error)

	FetchProduction(productionId int) (model.ProductionEntity, error)
	FetchProductionDetail(productionId int) ([]model.ProductionDetailEntity, error)
	FetchProductionCost(productionId int) ([]model.ProductionCostEntity, error)

	// Trans type conversion methods
	FetchJournalsForTransTypeConversion(limit int) ([]string, error)
	UpdateTransType(ids []string, newTransType int) error
}
