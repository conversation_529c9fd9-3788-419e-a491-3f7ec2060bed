package mysql

import (
	"fmt"
	"reflect"
	"testing"

	"github.com/joho/godotenv"
	"gitlab.com/uniqdev/backend/service-finance/app/config"
	mysql "gitlab.com/uniqdev/backend/service-finance/core/mysql"
	domain "gitlab.com/uniqdev/backend/service-finance/domain"
)

func Test_journalRepository_FetchProduct(t *testing.T) {
	godotenv.Load("/Users/<USER>/Documents/WORK/api-finance/.env")
	dbConn := config.GetMySqlConn()

	type fields struct {
		db mysql.Repository
	}
	type args struct {
		productIds []int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []domain.Product
		wantErr bool
	}{
		{"test1", fields{db: mysql.Repository{Conn: dbConn}}, args{[]int{6718}}, []domain.Product{}, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			j := journalRepository{
				db: tt.fields.db,
			}
			got, err := j.FetchProduct(tt.args.productIds...)
			fmt.Println("product: ", got)
			if (err != nil) != tt.wantErr {
				t.Errorf("journalRepository.FetchProduct() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("journalRepository.FetchProduct() = %v, want %v", got, tt.want)
			}
		})
	}
}
