package mysql

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/service-finance/core/log"
	mysql "gitlab.com/uniqdev/backend/service-finance/core/mysql"
	"gitlab.com/uniqdev/backend/service-finance/core/util/cast"
	"gitlab.com/uniqdev/backend/service-finance/core/util/collection"
	domain "gitlab.com/uniqdev/backend/service-finance/domain"
	"gitlab.com/uniqdev/backend/service-finance/model"
	"gitlab.com/uniqdev/backend/service-finance/module/journal"
)

type journalRepository struct {
	db mysql.Repository
}

func NewMysqlJournalRepository(db *sql.DB) journal.Repository {
	return &journalRepository{mysql.Repository{Conn: db}}
}

func (j journalRepository) InsertJournal(journal domain.Journal) {
	mapData := cast.ToMap(journal)
	mapData["created_at"] = time.Now().Unix() * 1000
	mapData["updated_at"] = time.Now().Unix() * 1000

	_, err := j.db.Insert("finance_jurnal_umum", mapData)
	log.IfError(err)
}

func (j journalRepository) InsertJournals(journals ...domain.Journal) {
	if len(journals) == 0 {
		fmt.Println("No journals to insert")
		return
	}

	//get unique trans id
	idsMap := make(map[string]domain.Journal)
	for _, journal := range journals {
		idsMap[journal.TransId] = journal
	}

	err := j.db.WithTransaction(func(tx mysql.Transaction) error {
		//first remove old data (to prevent duplication)
		for _, journal := range idsMap {
			log.Info("delete journal of '%v' id: %v", journal.TransType, journal.TransId)
			tx.Delete("finance_jurnal_umum", "trans_id = ? and trans_type = ?", journal.TransId, journal.TransType)
		}

		//then insert new data
		log.Info("inserting %d journals", len(journals))
		for _, journal := range journals {
			if journal.Nominal == 0 {
				continue
			}

			mapData := cast.ToMap(journal)
			mapData["created_at"] = time.Now().UnixMilli()
			mapData["updated_at"] = time.Now().UnixMilli()
			_ = tx.Insert("finance_jurnal_umum", mapData)
			// id, _ := res.LastInsertId()
			// fmt.Printf("%s : %v\n", journal.AccountName, id)
		}
		return nil
	})

	log.IfError(err)
}

func (j journalRepository) GetPaymentAndSales(chanPayment chan []domain.SalesWithPayment, salesId string) {
	sql := `select s.sales_id,
       s.status,
       s.discount,
       s.voucher,
       s.grand_total,
	   s.outlet_fkid,
		s.time_created,
       sp.method,
       sp.total,
       pmb.name as bank_name,
	   pmb.bank_id
from sales s
         join sales_payment sp on s.sales_id = sp.sales_fkid
         left join sales_payment_bank spb on sp.payment_id = spb.sales_payment_fkid
         left join payment_media_bank pmb on spb.bank_fkid = pmb.bank_id
where s.sales_id = ? `
	var sales []domain.SalesWithPayment

	q := j.db.Query(sql, salesId)
	fmt.Println("actual json: ", q.MustJson())
	err := q.Model(&sales)
	log.IfError(err)
	chanPayment <- sales
}

func (j journalRepository) GetTax(chanTax chan []domain.TaxWithGratuity, salesId string) {
	sql := `select st.total, st.category, g.name, g.gratuity_id
from sales_tax st
         join gratuity g on g.gratuity_id = st.tax_fkid
where st.sales_fkid = ?`
	q := j.db.Query(sql, salesId)
	log.IfError(q.Error)

	var tax []domain.TaxWithGratuity
	err := q.Model(&tax)
	log.IfError(err)

	chanTax <- tax
}

func (j journalRepository) GetSalesDetail(chanProducts chan []domain.SalesDetail, salesId string) {
	sql := `select sd.qty - coalesce(sv.qty, 0)                                                as qty,
       sd.price,
       coalesce(price_buy_total/(sd.qty - coalesce(sv.qty, 0)), price_buy) as price_buy,
       sd.discount - coalesce(sv.discount, 0)                                                 as discount,
       sd.product_fkid,
       sd.product_detail_fkid,
       (sd.price * (sd.qty - coalesce(sv.qty, 0))) - (sd.discount - coalesce(sv.discount, 0)) as sub_total,
       sd.sales_detail_id
from sales_detail sd
         left join (select sum(qty) as qty, sum(discount) as discount, sales_detail_fkid
                    from sales_void
                    group by sales_detail_fkid) sv on sd.sales_detail_id = sv.sales_detail_fkid
where sd.sales_fkid = ?
  and (sd.qty - coalesce(sv.qty, 0)) > 0 `
	q := j.db.Query(sql, salesId)
	log.IfError(q.Error)

	var detail []domain.SalesDetail
	err := q.Model(&detail)
	log.IfError(err)

	chanProducts <- detail
}

func (j journalRepository) GetTotalPromotion(chanTotalPromotion chan int, salesId string) {
	sql := `select promotion_value
from sales_promotion
where sales_fkid = ?`
	var promotion []domain.Promotion
	err := j.db.Query(sql, salesId).Model(&promotion)
	log.IfError(err)

	sql = `select sum(promotion_value) as promotion_value
from sales_detail_promotion
where sales_fkid = ? `
	var promotionDetail []domain.Promotion
	err = j.db.Query(sql, salesId).Model(&promotionDetail)
	log.IfError(err)

	total := 0
	for _, promo := range promotion {
		total += promo.PromotionValue
	}
	for _, promo := range promotionDetail {
		total += promo.PromotionValue
	}
	chanTotalPromotion <- total
}

func (j journalRepository) GetDefaultAccount() []domain.CoaJournal {
	var result []domain.CoaJournal
	err := j.db.Query(`SELECT
	fac.id,
	fac.nama_akun,
	fac.lock,
	fac.bertambah,
	fac.berkurang,
	COALESCE(fa.code, fac.kode_akun) AS kode_akun,
	fa.account_category_fkid
FROM
	finance_accounts_default fac
	LEFT JOIN finance_accounts fa ON fa.name = fac.nama_akun 
	order by fac.id `).Model(&result)
	log.IfError(err)
	return result
}

func (j journalRepository) GetAccountByProduct(ids []int) []domain.AccountSetting {
	var result []domain.AccountSetting
	if len(ids) == 0 {
		return result
	}

	// 	sql := `select fas.product_detail_fkid,
	//        fas.account_debit,
	//        facd.name as 'debit_name',
	//        fas.account_kredit,
	//        fack.name as 'credit_name'
	// from finance_accounts_setting fas
	//          join finance_accounts_category facd on facd.code = fas.account_debit
	//          join finance_accounts_category fack on fack.code = fas.account_kredit
	// 		 join finance_accounts fad on fad.account_id=fas.account_debit
	//          join finance_accounts fak on fak.account_id=fas.account_kredit
	// where fas.product_detail_fkid in ([IN])
	// order by product_detail_fkid`

	sql := `SELECT
	fas.product_fkid,
	fas.expenses,
	fad.code AS account_debit,
	fad.name AS 'debit_name',
	fak.code AS account_kredit,
	fak.name AS 'credit_name',
	fai.code AS 'inventory_code',
	fai.name as 'inventory_name',
	fad.account_category_fkid as debit_category_id,
	fak.account_category_fkid as credit_category_id,
	fai.account_category_fkid as inventory_category_id
FROM
	finance_account_setting fas
	JOIN finance_accounts fad ON fad.account_id = fas.account_debit
	JOIN finance_accounts fak ON fak.account_id = fas.account_kredit
	JOIN finance_accounts fai ON fai.account_id=fas.account_persediaan
WHERE
	fas.product_fkid in ([IN])
ORDER BY
	fas.product_fkid`
	sql = strings.Replace(sql, "[IN]", strings.Repeat("?,", len(ids)-1)+"?", 1)

	err := j.db.Query(sql, collection.ToInterfaceArray(ids)...).Model(&result)
	log.IfError(err)
	return result
}

func (j journalRepository) GetSalesPaymentById(paymentId int) domain.Payment {
	sql := `select sp.total, sp.method, spb.account_number, pmb.name as bank_name, sp.sales_fkid
from sales_payment sp
         left join sales_payment_bank spb on sp.payment_id = spb.sales_payment_fkid
         left join payment_media_bank pmb on spb.bank_fkid = pmb.bank_id
where sp.payment_id = ? `
	var result domain.Payment
	q := j.db.Query(sql, paymentId)
	err := q.Model(&result)
	log.IfError(err)
	return result
}

func (j journalRepository) GetAdminBySalesId(chanAdmin chan domain.Admin, salesId string) {
	sql := `select a.*
from admin a
         join outlets o on a.admin_id = o.admin_fkid
         join sales s on s.outlet_fkid = o.outlet_id
where s.sales_id = ?`
	var result domain.Admin
	err := j.db.Query(sql, salesId).Model(&result)
	log.IfError(err)
	chanAdmin <- result
}

func (j journalRepository) GetOutletAndAdminBySalesId(chanAdmin chan domain.OutletAndAdmin, salesId string) {
	sql := `select a.*, o.*
from admin a
         join outlets o on a.admin_id = o.admin_fkid
         join sales s on s.outlet_fkid = o.outlet_id
where s.sales_id = ?`
	var result domain.OutletAndAdmin
	err := j.db.Query(sql, salesId).Model(&result)
	log.IfError(err)
	chanAdmin <- result
}

func (j journalRepository) InsertJournalFailures(failures domain.JournalFailures) {
	_, err := j.db.Insert("finance_failures", cast.ToMap(failures))
	log.IfError(err)
}

func (j journalRepository) DeleteJournal(transType string, transId string) error {
	log.Info("delete journal of '%s' id: %v", transType, transId)
	_, err := j.db.Delete("finance_jurnal_umum", "trans_type = ? and trans_id = ?", transType, transId)
	return err
}

func (j journalRepository) GetPurchaseById(purchaseId string) []domain.DataPurchase {
	sql := `select * from purchase where purchase_id = ?`
	var result []domain.DataPurchase
	err := j.db.Query(sql, purchaseId).Model(&result)
	log.IfError(err)
	return result
}

func (j journalRepository) GetPurchaseDetail(purchaseId string) []domain.PurchaseDetail {
	sql := `select 
	pp.purchase_fkid,
	pp.purchase_products_id,
	pp.products_fkid,
	p.catalogue_type,
	pp.qty_stok as qty_purchase,
	pp.price_nota,
	IFNULL((SELECT qty_arive FROM purchase_confrim pc WHERE purchase_confrim_id = (SELECT MAX(purchase_confrim_id) FROM purchase_confrim) AND purchase_product_fkid = pp.purchase_products_id),0) AS qty_arive,
	pp.tot_dis
	from purchase_products pp
	left join products_detail pd on pd.product_detail_id = pp.products_fkid 
	left join products p on p.product_id = pd.product_fkid 
	where pp.purchase_fkid = ?`
	var result []domain.PurchaseDetail
	err := j.db.Query(sql, purchaseId).Model(&result)
	log.IfError(err)
	return result
}

func (j journalRepository) GetSalesBreakdown(chanSalesBreakdown chan []domain.SalesBreakdown, salesId string) {
	sql := `
select sum(if(status = 'success', sb.qty_total, sb.qty_total * -1)) as qty_total,
       any_value(sb.price_buy)                                      as price_buy,
       sb.product_detail_fkid,
       any_value(sales_status)                                      as sales_status,
       any_value(sb.sales_detail_fkid)                              as sales_detail_fkid,
       pd.product_fkid
from sales_breakdown sb
         join sales_detail sd on sb.sales_detail_fkid = sd.sales_detail_id
         join products_detail pd on sb.product_detail_fkid = pd.product_detail_id
where sd.sales_fkid = ?
group by sb.product_detail_fkid `
	var result []domain.SalesBreakdown
	err := j.db.Query(sql, salesId).Model(&result)
	log.IfError(err)
	chanSalesBreakdown <- result
}

func (j journalRepository) FetchTransactionType(transactionType string) (int, error) {
	sql := "select * from finance_trans_type where name = ?"
	result, err := j.db.Query(sql, transactionType).Map()
	if err != nil {
		return 0, err
	}

	return cast.ToInt(result["id"]), nil
}

func (j journalRepository) FetchAccountDefault(adminId int) ([]domain.AccountDefault, error) {
	sql := `SELECT COALESCE(fa.code, fas.code) as code, COALESCE(fa.name, fas.name) as name, 
	COALESCE(fdk.idx_key, concat(type,'#',type_fkid)) as idx_key, 
	COALESCE(fa.account_category_fkid, fas.account_category_fkid) as account_category_fkid, fac.type,
	fac.type_fkid
		from finance_account_general fac 
		left join finance_default_key fdk on fdk.id=fac.default_key_fkid
		left join finance_accounts fa on fa.account_id=fac.account_fkid
		left join finance_accounts fas on fas.account_id=fac.sales_account_fkid
	where fac.admin_fkid = ? `
	//and fac.type='general'

	var result []domain.AccountDefault
	err := j.db.Query(sql, adminId).Model(&result)
	return result, err

	// return j.db.Query(sql, adminId).MapArray()
}

func (j journalRepository) FetchProduct(productIds ...int) ([]domain.Product, error) {
	sql := `SELECT product_id, name, stock_management from products `

	if len(productIds) > 0 {
		sql += " where product_id in @productId"
	}

	sql, params := mysql.MapParam(sql, map[string]interface{}{
		"productId": productIds,
	})

	var result []domain.Product
	err := j.db.Query(sql, params...).Model(&result)
	return result, err
}

func (j journalRepository) GetSales(chanSales chan domain.Sales, salesId string) {
	var result domain.Sales
	sql := `select * from sales where sales_id = ?`
	err := j.db.Query(sql, salesId).Model(&result)
	log.IfError(err)
	chanSales <- result
}

func (j journalRepository) FetchUserSubscription(adminId int, serviceType string) ([]model.SystemSubscribe, error) {
	sql := `SELECT * from system_subscribe ss 
	where admin_fkid=?
	and feature=?
	and service_time_start < UNIX_TIMESTAMP()*1000
	and service_time_expired > UNIX_TIMESTAMP()*1000`

	var result []model.SystemSubscribe
	err := j.db.Query(sql, adminId, serviceType).Model(&result)
	return result, err
}

func (repo journalRepository) FetchProductionDetail(productionID int) ([]model.ProductionDetailEntity, error) {
	sql := "SELECT * FROM production_detail WHERE production_fkid = ?"

	var result []model.ProductionDetailEntity
	err := repo.db.Query(sql, productionID).Model(&result)
	return result, err
}

func (repo journalRepository) FetchProductionCost(productionID int) ([]model.ProductionCostEntity, error) {
	sql := "SELECT * FROM production_detail_cost WHERE production_fkid = ?"

	var result []model.ProductionCostEntity
	err := repo.db.Query(sql, productionID).Model(&result)
	return result, err
}

func (repo journalRepository) FetchProduction(productionID int) (model.ProductionEntity, error) {
	sql := `SELECT *, pd.product_fkid FROM production p 
	JOIN products_detail pd ON p.product_detail_fkid = pd.product_detail_id
	 WHERE production_id = ? `

	var result model.ProductionEntity
	err := repo.db.Query(sql, productionID).Model(&result)
	return result, err
}

func (j journalRepository) DeleteJournalFailure(transId string) error {
	log.Info("DeleteJournalFailure: %v", transId)
	_, err := j.db.Delete("finance_failures", "trans_id = ?", transId)
	return err
}

func (j journalRepository) FetchJournalFailure() (*[]domain.JournalFailures, error) {
	var result []domain.JournalFailures
	err := j.db.Query("select * from finance_failures").Model(&result)
	return &result, err
}

// GetSalesRefund implements journal.Repository.
func (j *journalRepository) GetSalesRefund(chanRefund chan domain.SalesRefund, salesId string) {
	var result domain.SalesRefund
	err := j.db.Query("select * from sales_refund where sales_fkid = ? ", salesId).Model(&result)
	log.IfError(err)
	chanRefund <- result
}

type TransactionData struct {
	TransID string `db:"trans_id"`
	Type    string `db:"type"`
}

func (j journalRepository) GetMissingJournals(startTime int64, endTime int64, adminId int) ([]string, error) {
	query := `
		SELECT DISTINCT trans_id, 'finance' as 'type' 
		FROM finance_jurnal_umum 
		WHERE trans_created BETWEEN ? AND ?
		AND trans_type = 1
		UNION
		SELECT s.sales_id, 'sales' 
		FROM sales s 
		JOIN outlets ON s.outlet_fkid = outlets.outlet_id
		WHERE s.time_created BETWEEN ? AND ?
		AND s.status = 'Success'
		AND outlets.admin_fkid = ?`

	var transactions []domain.TransactionData
	err := j.db.Query(query, startTime, endTime, startTime, endTime, adminId).Model(&transactions)
	if err != nil {
		return nil, err
	}

	// Create maps to track transactions
	financeMap := make(map[string]bool)
	var missingJournals []string

	// First pass: mark all finance transactions
	for _, t := range transactions {
		if t.Type == "finance" {
			financeMap[t.TransID] = true
		}
	}

	// Second pass: find sales without finance entries
	for _, t := range transactions {
		if t.Type == "sales" && !financeMap[t.TransID] {
			missingJournals = append(missingJournals, t.TransID)
		}
	}

	return missingJournals, nil
}

// FetchJournalsForTransTypeConversion fetches IDs of journals that need trans_type conversion
func (j journalRepository) FetchJournalsForTransTypeConversion(limit int) ([]string, error) {
	sql := `SELECT id FROM finance_jurnal_umum WHERE trans_type = 1 AND trans_id LIKE '%.%' LIMIT ?`

	var ids []string
	err := j.db.Query(sql, limit).Model(&ids)
	if err != nil {
		return nil, err
	}

	return ids, nil
}

// UpdateTransType updates the trans_type for the given journal IDs
func (j journalRepository) UpdateTransType(ids []string, newTransType int) error {
	if len(ids) == 0 {
		return nil
	}

	// Build the SQL with placeholders for the IDs
	placeholders := strings.Repeat("?,", len(ids)-1) + "?"
	sql := fmt.Sprintf("UPDATE finance_jurnal_umum SET trans_type = ? WHERE id IN (%s)", placeholders)

	// Prepare parameters: newTransType first, then all IDs
	params := make([]interface{}, 0, len(ids)+1)
	params = append(params, newTransType)
	for _, id := range ids {
		params = append(params, id)
	}

	_, err := j.db.Conn.Exec(sql, params...)
	return err
}
