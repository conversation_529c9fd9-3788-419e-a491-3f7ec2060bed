// Code generated by mockery v2.30.16. DO NOT EDIT.

package mocks

import (
	domain "gitlab.com/uniqdev/backend/service-finance/domain"

	mock "github.com/stretchr/testify/mock"

	model "gitlab.com/uniqdev/backend/service-finance/model"
)

// Repository is an autogenerated mock type for the Repository type
type Repository struct {
	mock.Mock
}

// DeleteJournal provides a mock function with given fields: transType, transId
func (_m *Repository) DeleteJournal(transType string, transId string) error {
	ret := _m.Called(transType, transId)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = rf(transType, transId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FetchAccountDefault provides a mock function with given fields: adminId
func (_m *Repository) FetchAccountDefault(adminId int) ([]domain.AccountDefault, error) {
	ret := _m.Called(adminId)

	var r0 []domain.AccountDefault
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]domain.AccountDefault, error)); ok {
		return rf(adminId)
	}
	if rf, ok := ret.Get(0).(func(int) []domain.AccountDefault); ok {
		r0 = rf(adminId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.AccountDefault)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(adminId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchProduct provides a mock function with given fields: productId
func (_m *Repository) FetchProduct(productId ...int) ([]domain.Product, error) {
	_va := make([]interface{}, len(productId))
	for _i := range productId {
		_va[_i] = productId[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []domain.Product
	var r1 error
	if rf, ok := ret.Get(0).(func(...int) ([]domain.Product, error)); ok {
		return rf(productId...)
	}
	if rf, ok := ret.Get(0).(func(...int) []domain.Product); ok {
		r0 = rf(productId...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Product)
		}
	}

	if rf, ok := ret.Get(1).(func(...int) error); ok {
		r1 = rf(productId...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchProduction provides a mock function with given fields: productionId
func (_m *Repository) FetchProduction(productionId int) (model.ProductionEntity, error) {
	ret := _m.Called(productionId)

	var r0 model.ProductionEntity
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (model.ProductionEntity, error)); ok {
		return rf(productionId)
	}
	if rf, ok := ret.Get(0).(func(int) model.ProductionEntity); ok {
		r0 = rf(productionId)
	} else {
		r0 = ret.Get(0).(model.ProductionEntity)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(productionId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchProductionCost provides a mock function with given fields: productionId
func (_m *Repository) FetchProductionCost(productionId int) ([]model.ProductionCostEntity, error) {
	ret := _m.Called(productionId)

	var r0 []model.ProductionCostEntity
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]model.ProductionCostEntity, error)); ok {
		return rf(productionId)
	}
	if rf, ok := ret.Get(0).(func(int) []model.ProductionCostEntity); ok {
		r0 = rf(productionId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.ProductionCostEntity)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(productionId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchProductionDetail provides a mock function with given fields: productionId
func (_m *Repository) FetchProductionDetail(productionId int) ([]model.ProductionDetailEntity, error) {
	ret := _m.Called(productionId)

	var r0 []model.ProductionDetailEntity
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]model.ProductionDetailEntity, error)); ok {
		return rf(productionId)
	}
	if rf, ok := ret.Get(0).(func(int) []model.ProductionDetailEntity); ok {
		r0 = rf(productionId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.ProductionDetailEntity)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(productionId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchTransactionType provides a mock function with given fields: transactionType
func (_m *Repository) FetchTransactionType(transactionType string) (int, error) {
	ret := _m.Called(transactionType)

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (int, error)); ok {
		return rf(transactionType)
	}
	if rf, ok := ret.Get(0).(func(string) int); ok {
		r0 = rf(transactionType)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(transactionType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchUserSubscription provides a mock function with given fields: adminId, serviceType
func (_m *Repository) FetchUserSubscription(adminId int, serviceType string) ([]model.SystemSubscribe, error) {
	ret := _m.Called(adminId, serviceType)

	var r0 []model.SystemSubscribe
	var r1 error
	if rf, ok := ret.Get(0).(func(int, string) ([]model.SystemSubscribe, error)); ok {
		return rf(adminId, serviceType)
	}
	if rf, ok := ret.Get(0).(func(int, string) []model.SystemSubscribe); ok {
		r0 = rf(adminId, serviceType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.SystemSubscribe)
		}
	}

	if rf, ok := ret.Get(1).(func(int, string) error); ok {
		r1 = rf(adminId, serviceType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAccountByProduct provides a mock function with given fields: productDetailIds
func (_m *Repository) GetAccountByProduct(productDetailIds []int) []domain.AccountSetting {
	ret := _m.Called(productDetailIds)

	var r0 []domain.AccountSetting
	if rf, ok := ret.Get(0).(func([]int) []domain.AccountSetting); ok {
		r0 = rf(productDetailIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.AccountSetting)
		}
	}

	return r0
}

// GetAdminBySalesId provides a mock function with given fields: chanAdmin, salesId
func (_m *Repository) GetAdminBySalesId(chanAdmin chan domain.Admin, salesId string) {
	_m.Called(chanAdmin, salesId)
}

// GetDefaultAccount provides a mock function with given fields:
func (_m *Repository) GetDefaultAccount() []domain.CoaJournal {
	ret := _m.Called()

	var r0 []domain.CoaJournal
	if rf, ok := ret.Get(0).(func() []domain.CoaJournal); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.CoaJournal)
		}
	}

	return r0
}

// GetOutletAndAdminBySalesId provides a mock function with given fields: _a0, _a1
func (_m *Repository) GetOutletAndAdminBySalesId(_a0 chan domain.OutletAndAdmin, _a1 string) {
	_m.Called(_a0, _a1)
}

// GetPaymentAndSales provides a mock function with given fields: chanPayment, salesId
func (_m *Repository) GetPaymentAndSales(chanPayment chan []domain.SalesWithPayment, salesId string) {
	_m.Called(chanPayment, salesId)
}

// GetPurchaseById provides a mock function with given fields: purchaseId
func (_m *Repository) GetPurchaseById(purchaseId string) []domain.DataPurchase {
	ret := _m.Called(purchaseId)

	var r0 []domain.DataPurchase
	if rf, ok := ret.Get(0).(func(string) []domain.DataPurchase); ok {
		r0 = rf(purchaseId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.DataPurchase)
		}
	}

	return r0
}

// GetPurchaseDetail provides a mock function with given fields: purchaseId
func (_m *Repository) GetPurchaseDetail(purchaseId string) []domain.PurchaseDetail {
	ret := _m.Called(purchaseId)

	var r0 []domain.PurchaseDetail
	if rf, ok := ret.Get(0).(func(string) []domain.PurchaseDetail); ok {
		r0 = rf(purchaseId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.PurchaseDetail)
		}
	}

	return r0
}

// GetSales provides a mock function with given fields: chatSales, salesId
func (_m *Repository) GetSales(chatSales chan domain.Sales, salesId string) {
	_m.Called(chatSales, salesId)
}

// GetSalesBreakdown provides a mock function with given fields: chanSalesBreakdown, salesId
func (_m *Repository) GetSalesBreakdown(chanSalesBreakdown chan []domain.SalesBreakdown, salesId string) {
	_m.Called(chanSalesBreakdown, salesId)
}

// GetSalesDetail provides a mock function with given fields: chanProducts, salesId
func (_m *Repository) GetSalesDetail(chanProducts chan []domain.SalesDetail, salesId string) {
	_m.Called(chanProducts, salesId)
}

// GetSalesPaymentById provides a mock function with given fields: paymentId
func (_m *Repository) GetSalesPaymentById(paymentId int) domain.Payment {
	ret := _m.Called(paymentId)

	var r0 domain.Payment
	if rf, ok := ret.Get(0).(func(int) domain.Payment); ok {
		r0 = rf(paymentId)
	} else {
		r0 = ret.Get(0).(domain.Payment)
	}

	return r0
}

// GetTax provides a mock function with given fields: chanTax, salesId
func (_m *Repository) GetTax(chanTax chan []domain.TaxWithGratuity, salesId string) {
	_m.Called(chanTax, salesId)
}

// GetTotalPromotion provides a mock function with given fields: chanTotalPromotion, salesId
func (_m *Repository) GetTotalPromotion(chanTotalPromotion chan int, salesId string) {
	_m.Called(chanTotalPromotion, salesId)
}

// InsertJournal provides a mock function with given fields: _a0
func (_m *Repository) InsertJournal(_a0 domain.Journal) {
	_m.Called(_a0)
}

// InsertJournalFailures provides a mock function with given fields: failures
func (_m *Repository) InsertJournalFailures(failures domain.JournalFailures) {
	_m.Called(failures)
}

// InsertJournals provides a mock function with given fields: _a0
func (_m *Repository) InsertJournals(_a0 ...domain.Journal) {
	_va := make([]interface{}, len(_a0))
	for _i := range _a0 {
		_va[_i] = _a0[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	_m.Called(_ca...)
}

// NewRepository creates a new instance of Repository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *Repository {
	mock := &Repository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
