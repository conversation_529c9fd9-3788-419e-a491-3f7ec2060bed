// Code generated by mockery v2.15.0. DO NOT EDIT.

package mocks

import (
	domain "gitlab.com/uniqdev/backend/service-finance/domain"

	mock "github.com/stretchr/testify/mock"
)

// JournalUseCase is an autogenerated mock type for the JournalUseCase type
type JournalUseCase struct {
	mock.Mock
}

// GetPurchase provides a mock function with given fields: purchaseId
func (_m *JournalUseCase) GetPurchase(purchaseId string) []domain.DataPurchase {
	ret := _m.Called(purchaseId)

	var r0 []domain.DataPurchase
	if rf, ok := ret.Get(0).(func(string) []domain.DataPurchase); ok {
		r0 = rf(purchaseId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.DataPurchase)
		}
	}

	return r0
}

// ReceiveNewSales provides a mock function with given fields: salesId
func (_m *JournalUseCase) ReceiveNewSales(salesId string) {
	_m.Called(salesId)
}

// ReceivePiutangPayment provides a mock function with given fields: paymentId
func (_m *JournalUseCase) ReceivePiutangPayment(paymentId int) {
	_m.Called(paymentId)
}

// ReceiveSalesHppUpdate provides a mock function with given fields: salesId
func (_m *JournalUseCase) ReceiveSalesHppUpdate(salesId string) {
	_m.Called(salesId)
}

// RunJob provides a mock function with given fields:
func (_m *JournalUseCase) RunJob() {
	_m.Called()
}

type mockConstructorTestingTNewJournalUseCase interface {
	mock.TestingT
	Cleanup(func())
}

// NewJournalUseCase creates a new instance of JournalUseCase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewJournalUseCase(t mockConstructorTestingTNewJournalUseCase) *JournalUseCase {
	mock := &JournalUseCase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
