package pubsub

import (
	"encoding/json"
	"fmt"
	"os"

	"gitlab.com/uniqdev/backend/service-finance/core/google"
	"gitlab.com/uniqdev/backend/service-finance/core/log"
	"gitlab.com/uniqdev/backend/service-finance/core/util/cast"
	"gitlab.com/uniqdev/backend/service-finance/module/journal"
)

type subscriber struct {
	journal journal.JournalUseCase
}

func NewPubSubSubscriber(journal journal.JournalUseCase) *subscriber {
	return &subscriber{journal}
}

func (s *subscriber) RunSubscription() {
	env := os.Getenv("ENV")
	subsId := fmt.Sprintf("pos-sales-%s-finance", env)
	go google.Subscribe(subsId, func(data []byte) bool {
		return s.receiveNewSales(data)
	})

	subsId = fmt.Sprintf("sales_piutang_%s-finance", env)
	go google.Subscribe(subsId, func(data []byte) bool {
		return s.receiveNewSalesPiutang(data)
	})

	subsId = fmt.Sprintf("hpp-update-%s-finance", env)
	go google.Subscribe(subsId, func(data []byte) bool {
		return s.receiveSalesHppUpdate(data)
	})

	subsId = fmt.Sprintf("production-%s-finance", env)
	go google.Subscribe(subsId, func(data []byte) bool {
		return s.receiveProduction(data)
	})

	// s.journal.ReceiveNewProduction(132)
}

func (s *subscriber) receiveNewSales(data []byte) bool {
	var sales struct {
		SalesId  string `json:"sales_id"`
		Status   string `json:"status"`
		OutletId int    `json:"outlet_id"`
		AdminId  int    `json:"admin_id"`
	}
	log.IfError(json.Unmarshal(data, &sales))
	fmt.Println("new sales : ", sales.SalesId)
	s.journal.ReceiveNewSales(sales.SalesId)
	return true
}

func (s *subscriber) receiveNewSalesPiutang(data []byte) bool {
	var piutang struct {
		SalesId   string `json:"sales_id"`
		PiutangId int    `json:"piutang_id"`
		PaymentId int    `json:"payment_id"`
		AdminId   int    `json:"admin_id"`
	}
	log.IfError(json.Unmarshal(data, &piutang))
	s.journal.ReceivePiutangPayment(piutang.PaymentId)
	return true
}

func (s *subscriber) receiveSalesHppUpdate(data []byte) bool {
	var hppUpdate struct {
		Source string `json:"source"`
		Id     string `json:"id"`
	}

	log.IfError(json.Unmarshal(data, &hppUpdate))
	fmt.Println("update hpp sales : ", hppUpdate.Id)

	//we only care if update is on sales
	if hppUpdate.Source != "sales" {
		return true
	}

	s.journal.ReceiveSalesHppUpdate(hppUpdate.Id)
	return true
}

func (s *subscriber) receiveProduction(data []byte) bool {
	fmt.Println("receive new production: ", string(data))
	var production struct {
		AdminID      string `json:"admin_id"`
		OutletID     string `json:"outlet_id"`
		ProductionID string `json:"production_id"`
	}
	log.IfError(json.Unmarshal(data, &production))
	fmt.Println("productionId: ", production.ProductionID)
	s.journal.ReceiveNewProduction(cast.ToInt(production.ProductionID))

	return true
}
