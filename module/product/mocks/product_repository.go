// Code generated by mockery v2.30.16. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"
	model "gitlab.com/uniqdev/backend/service-finance/model"
)

// Repository is an autogenerated mock type for the Repository type
type Repository struct {
	mock.Mock
}

// FetchProductDetail provides a mock function with given fields: ids
func (_m *Repository) FetchProductDetail(ids ...int) ([]model.ProductDetailEntity, error) {
	_va := make([]interface{}, len(ids))
	for _i := range ids {
		_va[_i] = ids[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []model.ProductDetailEntity
	var r1 error
	if rf, ok := ret.Get(0).(func(...int) ([]model.ProductDetailEntity, error)); ok {
		return rf(ids...)
	}
	if rf, ok := ret.Get(0).(func(...int) []model.ProductDetailEntity); ok {
		r0 = rf(ids...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.ProductDetailEntity)
		}
	}

	if rf, ok := ret.Get(1).(func(...int) error); ok {
		r1 = rf(ids...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchPurchaseReportCategory provides a mock function with given fields: id
func (_m *Repository) FetchPurchaseReportCategory(id []int) ([]model.PurchaseReportCategoryEntity, error) {
	ret := _m.Called(id)

	var r0 []model.PurchaseReportCategoryEntity
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]model.PurchaseReportCategoryEntity, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func([]int) []model.PurchaseReportCategoryEntity); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.PurchaseReportCategoryEntity)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewRepository creates a new instance of Repository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *Repository {
	mock := &Repository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
