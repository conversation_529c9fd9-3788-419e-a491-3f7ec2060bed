package mysql

import (
	"database/sql"

	mysql "gitlab.com/uniqdev/backend/service-finance/core/mysql"
	"gitlab.com/uniqdev/backend/service-finance/model"
	"gitlab.com/uniqdev/backend/service-finance/module/product"
)

type productRepository struct {
	mysql.Repository
}

func NewMysqlProductRepository(db *sql.DB) product.Repository {
	return &productRepository{mysql.Repository{Conn: db}}
}

func (p productRepository) FetchProductDetail(ids ...int) ([]model.ProductDetailEntity, error) {
	sql := `select * from products_detail where product_detail_id in @ids`
	sql, params := mysql.MapParam(sql, map[string]interface{}{
		"ids": ids,
	})

	var result []model.ProductDetailEntity
	err := p.Query(sql, params...).Model(&result)
	return result, err
}

func (p productRepository) FetchPurchaseReportCategory(ids []int) ([]model.PurchaseReportCategoryEntity, error) {
	sql := `select * from purchase_report_category where purchase_report_category_id in @ids`
	sql, params := mysql.MapParam(sql, map[string]interface{}{
		"ids": ids,
	})
	var result []model.PurchaseReportCategoryEntity
	err := p.Query(sql, params...).Model(&result)
	return result, err
}
